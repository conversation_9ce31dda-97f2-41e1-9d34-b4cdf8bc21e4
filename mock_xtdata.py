#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟XtData模块

用于测试XtData适配器的逻辑，无需真实的MiniQMT客户端
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional


class MockXtData:
    """模拟的XtData模块"""
    
    def __init__(self):
        self.connected = False
        
        # 模拟股票列表
        self.mock_stocks = [
            "000001.SZ", "000002.SZ", "000858.SZ", "000876.SZ", "002415.SZ",
            "600000.SH", "600036.SH", "600519.SH", "600887.SH", "601318.SH"
        ]
        
        # 模拟行业板块列表（申万一级行业分类）
        self.mock_industry_sectors = [
            "银行", "非银金融", "房地产", "建筑装饰", "建筑材料",
            "钢铁", "有色金属", "煤炭", "石油石化", "化工",
            "基础化工", "医药生物", "食品饮料", "农林牧渔", "纺织服装",
            "轻工制造", "商贸零售", "交通运输", "汽车", "家用电器",
            "电力设备", "机械设备", "国防军工", "电子", "计算机",
            "通信", "传媒", "电力公用事业", "环保", "美容护理",
            "社会服务", "综合"
        ]

        # 模拟概念板块列表
        self.mock_concept_sectors = [
            "新能源", "5G", "人工智能", "区块链", "物联网",
            "芯片", "半导体", "新基建", "碳中和", "数字货币",
            "工业互联网", "云计算", "大数据", "虚拟现实", "增强现实",
            "自动驾驶", "新能源汽车", "锂电池", "光伏", "风电",
            "储能", "氢能源", "核电", "特高压", "充电桩",
            "工业4.0", "智能制造", "机器人", "无人机", "3D打印",
            "生物医药", "基因测序", "细胞治疗", "医疗器械", "互联网医疗",
            "在线教育", "直播电商", "新零售", "共享经济", "数字经济",
            "网络安全", "信息安全", "量子通信", "卫星导航", "航天航空",
            "海洋经济", "乡村振兴", "京津冀", "长三角", "粤港澳大湾区",
            "一带一路", "自贸区", "雄安新区", "海南自贸港", "成渝双城",
            "RCEP", "专精特新", "北交所", "注册制", "REITs"
        ]

        # 合并所有板块（保持向后兼容）
        self.mock_sectors = self.mock_industry_sectors + self.mock_concept_sectors
    
    def reconnect(self, ip: str, port: int):
        """模拟重连"""
        print(f"Mock: 连接到 {ip}:{port}")
        self.connected = True
    
    def get_instrument_detail(self, symbol: str) -> Optional[Dict]:
        """模拟获取合约详情"""
        if symbol in self.mock_stocks:
            return {
                'InstrumentID': symbol,
                'InstrumentName': f"模拟股票{symbol[:6]}",
                'ExchangeID': symbol.split('.')[1]
            }
        return None
    
    def download_sector_data(self):
        """模拟下载板块数据"""
        print("Mock: 下载板块分类信息")
    
    def download_index_weight(self):
        """模拟下载指数权重"""
        print("Mock: 下载指数成分权重信息")
    
    def get_market_data(
        self,
        field_list: List[str],
        stock_list: List[str],
        period: str,
        start_time: str,
        end_time: str,
        count: int = -1,
        dividend_type: str = 'none',
        fill_data: bool = True
    ) -> Dict:
        """模拟获取市场数据"""
        if not stock_list:
            return {}
        
        symbol = stock_list[0]
        
        # 生成模拟的时间序列
        start_date = datetime.strptime(start_time, '%Y%m%d')
        end_date = datetime.strptime(end_time, '%Y%m%d')
        
        # 生成交易日（排除周末）
        dates = []
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 周一到周五
                dates.append(current_date)
            current_date += timedelta(days=1)
        
        if not dates:
            return {}
        
        # 生成模拟价格数据
        num_days = len(dates)
        base_price = 10.0
        
        # 生成随机价格序列
        np.random.seed(42)  # 固定种子以获得可重复的结果
        price_changes = np.random.normal(0, 0.02, num_days)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 0.1))  # 确保价格不为负
        
        # 生成OHLC数据
        opens = []
        highs = []
        lows = []
        closes = prices
        volumes = []
        amounts = []
        
        for i, close_price in enumerate(closes):
            # 生成开盘价（基于前一日收盘价）
            if i == 0:
                open_price = close_price
            else:
                open_price = closes[i-1] * (1 + np.random.normal(0, 0.01))
            
            # 生成最高价和最低价
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.01)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.01)))
            
            # 生成成交量和成交额
            volume = int(np.random.uniform(1000000, 10000000))
            amount = volume * close_price
            
            opens.append(open_price)
            highs.append(high_price)
            lows.append(low_price)
            volumes.append(volume)
            amounts.append(amount)
        
        # 转换为时间戳
        timestamps = [int(date.timestamp()) for date in dates]
        
        return {
            'time': timestamps,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'amount': amounts
        }
    
    def download_history_data(
        self,
        stock_code: str,
        period: str,
        start_time: str,
        end_time: str,
        incrementally: bool = True
    ):
        """模拟下载历史数据"""
        print(f"Mock: 下载 {stock_code} 历史数据 ({period}, {start_time}-{end_time})")
    
    def download_history_data2(
        self,
        stock_list: List[str],
        period: str,
        start_time: str,
        end_time: str,
        callback=None,
        incrementally: bool = True
    ):
        """模拟批量下载历史数据"""
        print(f"Mock: 批量下载 {len(stock_list)} 只股票历史数据")
        
        if callback:
            for i, stock in enumerate(stock_list):
                callback({
                    'finished': i + 1,
                    'total': len(stock_list),
                    'stockcode': stock
                })
    
    def get_sector_list(self, sector_type: str = "industry") -> List[str]:
        """模拟获取板块列表"""
        if sector_type == "industry":
            return self.mock_industry_sectors
        elif sector_type == "concept":
            return self.mock_concept_sectors
        else:
            return self.mock_sectors  # 返回所有板块
    
    def get_stock_list_in_sector(self, sector_name: str) -> List[str]:
        """模拟获取板块成分股"""
        if sector_name in ["沪深A股", "上海A股", "深圳A股"]:
            return self.mock_stocks
        elif sector_name in self.mock_sectors:
            # 返回部分股票作为该板块的成分股
            return self.mock_stocks[:5]
        return []

    def get_sector_stocks(self, sector_code: str) -> List[str]:
        """模拟获取板块成分股（别名方法）"""
        return self.get_stock_list_in_sector(sector_code)

    def get_industry_list(self) -> List[str]:
        """模拟获取行业板块列表"""
        return self.mock_industry_sectors

    def get_concept_list(self) -> List[str]:
        """模拟获取概念板块列表"""
        return self.mock_concept_sectors
    
    def get_trading_calendar(
        self,
        market: str,
        start_time: str,
        end_time: str
    ) -> List[str]:
        """模拟获取交易日历"""
        start_date = datetime.strptime(start_time, '%Y%m%d')
        end_date = datetime.strptime(end_time, '%Y%m%d')
        
        trading_dates = []
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 周一到周五
                trading_dates.append(current_date.strftime('%Y%m%d'))
            current_date += timedelta(days=1)
        
        return trading_dates


# 创建模拟模块实例
xtdata = MockXtData()


# 模拟模块级别的函数
def reconnect(ip: str, port: int):
    return xtdata.reconnect(ip, port)

def get_instrument_detail(symbol: str):
    return xtdata.get_instrument_detail(symbol)

def download_sector_data():
    return xtdata.download_sector_data()

def download_index_weight():
    return xtdata.download_index_weight()

def get_market_data(*args, **kwargs):
    return xtdata.get_market_data(*args, **kwargs)

def download_history_data(*args, **kwargs):
    return xtdata.download_history_data(*args, **kwargs)

def download_history_data2(*args, **kwargs):
    return xtdata.download_history_data2(*args, **kwargs)

def get_sector_list(sector_type: str = "industry"):
    return xtdata.get_sector_list(sector_type)

def get_stock_list_in_sector(sector_name: str):
    return xtdata.get_stock_list_in_sector(sector_name)

def get_sector_stocks(sector_code: str):
    return xtdata.get_sector_stocks(sector_code)

def get_industry_list():
    return xtdata.get_industry_list()

def get_concept_list():
    return xtdata.get_concept_list()

def get_trading_calendar(*args, **kwargs):
    return xtdata.get_trading_calendar(*args, **kwargs)
