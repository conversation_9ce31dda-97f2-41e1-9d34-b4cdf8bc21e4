#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XtData数据源适配器

基于xtquant库实现的数据源适配器，提供股票数据获取功能
"""

import time
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
import threading
import queue
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.exceptions import DataSourceError, ConnectionError
from .base import IDataSource, DataSourceConfig, MarketData

logger = get_logger(__name__)


class XtDataAdapter(IDataSource):
    """XtData数据源适配器"""
    
    def __init__(self, config: DataSourceConfig):
        """
        初始化XtData适配器
        
        Args:
            config: 数据源配置
        """
        super().__init__(config)
        
        # XtData相关
        self.xtdata = None
        self.session_id = None
        self._is_connected = False
        
        # 连接参数
        self.host = config.config.get('host', '127.0.0.1')
        self.port = config.config.get('port', 58610)
        self.username = config.config.get('username', '')
        self.password = config.config.get('password', '')
        
        # 数据缓存
        self.stock_list_cache = None
        self.cache_timestamp = None
        self.cache_duration = 3600  # 1小时缓存
        
        # 请求队列和限制
        self.request_queue = queue.Queue()
        self.max_requests_per_second = 10
        self.last_request_time = 0
        
        logger.info(f"XtData适配器初始化完成，连接地址: {self.host}:{self.port}")
    
    def connect(self) -> bool:
        """
        连接到XtData服务

        Returns:
            bool: 连接是否成功
        """
        try:
            # 尝试导入xtquant库
            try:
                import xtquant.xtdata as xtdata
                self.xtdata = xtdata
                logger.info("成功导入xtquant库")
            except ImportError as e:
                # 检查是否可以使用mock数据
                if self._should_use_mock_data():
                    logger.warning("xtquant库不可用，使用模拟数据模式")
                    return self._setup_mock_data()

                error_msg = """XtData模块不可用，请按以下步骤安装：

1. 安装xtquant库：
   pip install xtquant

2. 下载并安装XtData客户端：
   - 访问迅投官网下载XtData客户端
   - 安装并启动客户端
   - 确保客户端正在运行

3. 配置连接参数：
   - 服务器地址：127.0.0.1
   - 端口号：58610（默认）
   - 确保防火墙允许相关端口通信

4. 验证安装：
   - 重新启动本系统
   - 测试数据源连接

详细安装指南请参考系统帮助文档。"""
                logger.error(error_msg)
                raise ConnectionError(error_msg)
            
            # 连接到XtData服务
            logger.info(f"正在连接到XtData服务: {self.host}:{self.port}")

            # 根据官方文档，XtData不需要显式连接，直接测试功能即可
            # 如果有特定的连接方法，可以使用reconnect
            try:
                if hasattr(self.xtdata, 'reconnect'):
                    # 尝试连接到指定IP端口
                    result = self.xtdata.reconnect(self.host, self.port)
                    if result != 0:
                        logger.warning(f"reconnect返回非0值: {result}")

                # 测试基本功能来验证连接
                self._test_basic_functions()

                self._is_connected = True
                logger.info("XtData连接成功")
                return True

            except Exception as e:
                error_msg = f"XtData连接测试失败: {e}"
                logger.error(error_msg)
                raise ConnectionError(error_msg)

        except Exception as e:
            logger.error(f"连接XtData失败: {e}")
            self._is_connected = False
            raise ConnectionError(f"连接XtData失败: {e}")
    
    def disconnect(self) -> bool:
        """
        断开XtData连接
        
        Returns:
            bool: 断开是否成功
        """
        try:
            if self.xtdata and self._is_connected:
                # XtData没有显式的断开连接方法，只需要标记状态
                logger.info("XtData连接已断开")

            self._is_connected = False
            return True
            
        except Exception as e:
            logger.error(f"断开XtData连接失败: {e}")
            return False
    
    def _test_basic_functions(self):
        """测试基本功能"""
        try:
            # 测试获取股票列表
            logger.info("测试获取股票列表...")
            stock_list = self.xtdata.get_stock_list_in_sector("沪深A股")
            if stock_list and len(stock_list) > 0:
                logger.info(f"成功获取 {len(stock_list)} 只股票")
            else:
                logger.warning("获取股票列表为空")
            
            # 测试获取基本行情数据
            logger.info("测试获取基本行情数据...")
            test_symbol = "000001.SZ"
            if stock_list and len(stock_list) > 0:
                test_symbol = stock_list[0]
            
            # 获取最新行情
            quotes = self.xtdata.get_full_tick([test_symbol])
            if quotes and test_symbol in quotes:
                logger.info(f"成功获取 {test_symbol} 的行情数据")
            else:
                logger.warning(f"获取 {test_symbol} 行情数据失败")
                
        except Exception as e:
            logger.warning(f"基本功能测试出现问题: {e}")
    
    def get_stock_list(self) -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        Returns:
            List[Dict[str, Any]]: 股票列表
        """
        try:
            # 检查缓存
            if self._is_cache_valid():
                logger.info("使用缓存的股票列表")
                return self.stock_list_cache
            
            if not self._is_connected:
                raise ConnectionError("XtData未连接")
            
            logger.info("正在获取股票列表...")
            
            # 获取沪深A股列表
            stock_list = []
            
            # 分别获取沪市和深市股票
            sectors = ["沪深A股"]  # 可以根据需要添加更多板块
            
            for sector in sectors:
                try:
                    sector_stocks = self.xtdata.get_stock_list_in_sector(sector)
                    if sector_stocks:
                        stock_list.extend(sector_stocks)
                        logger.info(f"从 {sector} 获取到 {len(sector_stocks)} 只股票")
                except Exception as e:
                    logger.warning(f"获取 {sector} 股票列表失败: {e}")
            
            # 去重并格式化
            unique_stocks = list(set(stock_list))
            formatted_stocks = []
            
            for symbol in unique_stocks:
                try:
                    # 解析股票代码和市场
                    if '.' in symbol:
                        code, market = symbol.split('.')
                        market_name = "深市" if market == "SZ" else "沪市"
                        
                        # 获取股票名称（如果可能）
                        name = self._get_stock_name(symbol)
                        
                        formatted_stocks.append({
                            'symbol': symbol,
                            'code': code,
                            'name': name,
                            'market': market_name,
                            'exchange': market
                        })
                except Exception as e:
                    logger.warning(f"格式化股票 {symbol} 信息失败: {e}")
            
            # 缓存结果
            self.stock_list_cache = formatted_stocks
            self.cache_timestamp = time.time()
            
            logger.info(f"成功获取 {len(formatted_stocks)} 只股票")
            return formatted_stocks
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise DataSourceError(f"获取股票列表失败: {e}")
    
    def _get_stock_name(self, symbol: str) -> str:
        """
        获取股票名称

        Args:
            symbol: 股票代码

        Returns:
            str: 股票名称
        """
        try:
            # 如果是模拟数据模式，使用预定义的股票名称
            if self._should_use_mock_data() or not self.xtdata:
                return self._get_mock_stock_name(symbol)

            # 获取股票基本信息
            info = self.xtdata.get_instrument_detail(symbol)
            if info and 'InstrumentName' in info:
                return info['InstrumentName']
            else:
                # 如果无法获取名称，返回模拟名称
                return self._get_mock_stock_name(symbol)
        except Exception as e:
            logger.warning(f"获取股票 {symbol} 名称失败: {e}")
            return self._get_mock_stock_name(symbol)

    def _get_mock_stock_name(self, symbol: str) -> str:
        """获取模拟股票名称"""
        # 预定义一些常见股票的名称
        mock_names = {
            '000001.SZ': '平安银行',
            '000002.SZ': '万科A',
            '000858.SZ': '五粮液',
            '002415.SZ': '海康威视',
            '300059.SZ': '东方财富',
            '600000.SH': '浦发银行',
            '600036.SH': '招商银行',
            '600519.SH': '贵州茅台',
            '600887.SH': '伊利股份',
            '000858.SZ': '五粮液'
        }

        if symbol in mock_names:
            return mock_names[symbol]

        # 根据代码生成模拟名称
        code = symbol.split('.')[0]
        if symbol.endswith('.SH'):
            if code.startswith('60'):
                return f"沪市股票{code}"
            elif code.startswith('688'):
                return f"科创板{code}"
        elif symbol.endswith('.SZ'):
            if code.startswith('00'):
                return f"深市主板{code}"
            elif code.startswith('30'):
                return f"创业板{code}"
            elif code.startswith('002'):
                return f"中小板{code}"

        return code
    
    def get_market_data(self, symbol: Union[str, List[str]], period: str = "1d",
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None) -> MarketData:
        """
        获取市场数据

        Args:
            symbol: 股票代码或股票代码列表
            period: 时间周期
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            MarketData: 市场数据
        """
        try:
            if not self._is_connected:
                # 尝试重新连接
                logger.warning("XtData未连接，尝试重新连接...")
                if not self.connect():
                    raise ConnectionError("XtData连接失败，无法获取数据")

            # 速率限制
            self._rate_limit()

            # 处理单个股票和批量股票
            if isinstance(symbol, str):
                symbols = [symbol]
                single_symbol = symbol
            else:
                symbols = symbol
                single_symbol = symbols[0] if symbols else None

            if not symbols:
                raise DataSourceError("股票代码列表为空")

            # 设置默认日期范围并转换格式
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            else:
                # 转换日期格式为YYYYMMDD
                end_date = self._convert_date_format(end_date)

            if not start_date:
                # 对于实时数据，只获取最近几天的数据
                days = 5 if period == '1d' else 365
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            else:
                # 转换日期格式为YYYYMMDD
                start_date = self._convert_date_format(start_date)

            logger.info(f"获取 {len(symbols)} 只股票的市场数据，周期: {period}, 日期范围: {start_date} - {end_date}")

            # 转换周期格式
            period_map = {
                '1d': '1d',
                '1m': '1m',
                '5m': '5m',
                '15m': '15m',
                '30m': '30m',
                '60m': '1h',
                '1h': '1h'
            }

            xt_period = period_map.get(period, '1d')

            # 获取历史数据 - 使用get_market_data_ex方法
            logger.debug(f"调用XtData API: stock_list={symbols}, period={xt_period}, start_time={start_date}, end_time={end_date}")

            try:
                data = self.xtdata.get_market_data_ex(
                    stock_list=symbols,
                    period=xt_period,
                    start_time=start_date,
                    end_time=end_date,
                    count=-1,
                    dividend_type='none',
                    fill_data=True
                )

                # 检查API返回值
                if data is None:
                    logger.warning("XtData API返回None，可能是日期范围问题或股票代码错误")
                    # 尝试使用更宽的日期范围
                    fallback_start = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
                    fallback_end = datetime.now().strftime('%Y%m%d')

                    logger.info(f"尝试使用备用日期范围: {fallback_start} - {fallback_end}")
                    data = self.xtdata.get_market_data_ex(
                        stock_list=symbols,
                        period=xt_period,
                        start_time=fallback_start,
                        end_time=fallback_end,
                        count=-1,
                        dividend_type='none',
                        fill_data=True
                    )

            except Exception as api_error:
                logger.error(f"XtData API调用失败: {api_error}")
                # 检查是否是连接问题
                if "连接" in str(api_error) or "网络" in str(api_error) or "timeout" in str(api_error).lower():
                    raise ConnectionError(f"XtData连接异常: {api_error}")
                else:
                    raise DataSourceError(f"XtData API错误: {api_error}")

            # 详细检查返回的数据
            if data is None:
                logger.error("XtData返回None数据")
                raise DataSourceError(f"XtData返回空数据，可能的原因：1) 股票代码不存在 2) 日期范围无数据 3) 服务异常")

            if not isinstance(data, dict):
                logger.error(f"XtData返回数据类型异常: {type(data)}")
                raise DataSourceError(f"XtData返回数据格式异常: {type(data)}")

            logger.debug(f"XtData返回数据键: {list(data.keys()) if data else 'None'}")

            # 如果是单个股票，返回该股票的数据
            if isinstance(symbol, str):
                if symbol not in data:
                    available_symbols = list(data.keys()) if data else []
                    logger.error(f"股票 {symbol} 不在返回数据中，可用股票: {available_symbols}")
                    raise DataSourceError(f"未获取到 {symbol} 的数据，可能原因：1) 股票代码错误 2) 该股票在指定日期范围内无交易数据")

                df = data[symbol]
                if df is None or (hasattr(df, 'empty') and df.empty):
                    logger.warning(f"股票 {symbol} 在指定时间范围内无数据，尝试扩大时间范围")

                    # 尝试更大的时间范围
                    extended_start = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
                    extended_end = datetime.now().strftime('%Y%m%d')

                    logger.info(f"尝试扩展时间范围: {extended_start} - {extended_end}")
                    extended_data = self.xtdata.get_market_data_ex(
                        stock_list=[symbol],
                        period=xt_period,
                        start_time=extended_start,
                        end_time=extended_end,
                        count=-1,
                        dividend_type='none',
                        fill_data=True
                    )

                    if extended_data and symbol in extended_data and not extended_data[symbol].empty:
                        df = extended_data[symbol]
                        logger.info(f"使用扩展时间范围成功获取 {symbol} 数据")
                    else:
                        logger.warning(f"股票 {symbol} 历史数据不可用，尝试使用实时数据")
                        # 尝试使用实时数据作为替代
                        realtime_data = self._get_realtime_as_history(symbol)
                        if realtime_data is not None:
                            df = realtime_data
                            logger.info(f"使用实时数据替代 {symbol} 的历史数据")
                        else:
                            raise DataSourceError(f"股票 {symbol} 无可用数据（历史数据和实时数据都不可用）")
            else:
                # 如果是批量股票，合并所有数据
                df_list = []
                empty_symbols = []

                for sym in symbols:
                    if sym in data and data[sym] is not None:
                        sym_data = data[sym]
                        if hasattr(sym_data, 'empty') and not sym_data.empty:
                            sym_df = sym_data.copy()
                            sym_df['symbol'] = sym
                            df_list.append(sym_df)
                        else:
                            empty_symbols.append(sym)
                    else:
                        empty_symbols.append(sym)

                if empty_symbols:
                    logger.warning(f"以下股票无数据: {empty_symbols}")

                if df_list:
                    df = pd.concat(df_list, ignore_index=True)
                else:
                    raise DataSourceError(f"所有股票都无有效数据，检查的股票: {symbols}")

            # 检查DataFrame是否为空
            if df.empty:
                symbol_str = single_symbol if isinstance(symbol, str) else f"{len(symbols)}只股票"
                raise DataSourceError(f"获取到的 {symbol_str} 数据为空，可能原因：1) 指定日期范围内无交易 2) 股票停牌 3) 数据源问题")

            # 确保列名正确
            if 'open' not in df.columns:
                # 如果列名不同，尝试重命名
                column_mapping = {
                    'Open': 'open',
                    'High': 'high',
                    'Low': 'low',
                    'Close': 'close',
                    'Volume': 'volume',
                    'Amount': 'amount'
                }
                df = df.rename(columns=column_mapping)

            # 数据清洗
            original_len = len(df)
            df = df.dropna()
            if 'volume' in df.columns:
                df = df[df['volume'] > 0]  # 过滤无成交量的数据

            if len(df) < original_len:
                logger.debug(f"数据清洗：原始 {original_len} 行，清洗后 {len(df)} 行")

            symbol_str = single_symbol if isinstance(symbol, str) else f"{len(symbols)}只股票"
            logger.info(f"成功获取 {symbol_str} 的 {len(df)} 条数据记录")

            return MarketData(
                symbol=single_symbol if isinstance(symbol, str) else 'batch',
                data=df,
                source='xtdata',
                update_time=datetime.now(),
                data_type=period
            )

        except Exception as e:
            error_msg = f"获取 {symbol} 市场数据失败: {e}"
            logger.error(error_msg)

            # 根据错误类型创建相应的异常
            if isinstance(e, (ConnectionError, TimeoutError)):
                from ..utils.exceptions import ErrorCode
                raise DataSourceError(error_msg, error_code=ErrorCode.DATA_SOURCE_CONNECTION_ERROR)
            elif "认证" in str(e) or "授权" in str(e):
                from ..utils.exceptions import ErrorCode
                raise DataSourceError(error_msg, error_code=ErrorCode.DATA_SOURCE_AUTH_ERROR)
            elif isinstance(e, DataSourceError):
                raise e  # 重新抛出已经是DataSourceError的异常
            else:
                from ..utils.exceptions import ErrorCode
                raise DataSourceError(error_msg, error_code=ErrorCode.DATA_SOURCE_DATA_ERROR)
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        获取实时数据
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            Dict[str, Dict[str, Any]]: 实时数据
        """
        try:
            if not self._is_connected:
                raise ConnectionError("XtData未连接")
            
            # 速率限制
            self._rate_limit()
            
            logger.info(f"获取 {len(symbols)} 只股票的实时数据")
            
            # 获取实时行情
            quotes = self.xtdata.get_full_tick(symbols)
            
            if not quotes:
                raise DataSourceError("未获取到实时数据")
            
            # 格式化数据
            formatted_data = {}
            for symbol in symbols:
                if symbol in quotes:
                    quote = quotes[symbol]
                    formatted_data[symbol] = {
                        'last_price': quote.get('lastPrice', 0),
                        'change': quote.get('change', 0),
                        'change_percent': quote.get('changePercent', 0),
                        'volume': quote.get('volume', 0),
                        'amount': quote.get('amount', 0),
                        'open': quote.get('open', 0),
                        'high': quote.get('high', 0),
                        'low': quote.get('low', 0),
                        'prev_close': quote.get('preClose', 0),
                        'timestamp': quote.get('time', int(time.time() * 1000))
                    }
            
            logger.info(f"成功获取 {len(formatted_data)} 只股票的实时数据")
            return formatted_data
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            raise DataSourceError(f"获取实时数据失败: {e}")
    
    def get_sector_data(self, sector_type: str = "industry") -> List[Dict[str, Any]]:
        """
        获取板块数据

        Args:
            sector_type: 板块类型（industry: 行业板块, concept: 概念板块）

        Returns:
            List[Dict[str, Any]]: 板块数据
        """
        try:
            if not self._is_connected:
                raise ConnectionError("XtData未连接")

            logger.info(f"获取板块数据，类型: {sector_type}")

            # 获取板块列表
            if sector_type == "industry":
                # 尝试使用专门的行业板块方法
                if hasattr(self.xtdata, 'get_industry_list'):
                    sectors = self.xtdata.get_industry_list()
                else:
                    # 检查get_sector_list是否支持参数
                    try:
                        sectors = self.xtdata.get_sector_list("industry")
                    except TypeError:
                        # 如果不支持参数，获取所有板块
                        sectors = self.xtdata.get_sector_list()
            elif sector_type == "concept":
                # 尝试使用专门的概念板块方法
                if hasattr(self.xtdata, 'get_concept_list'):
                    sectors = self.xtdata.get_concept_list()
                else:
                    # 检查get_sector_list是否支持参数
                    try:
                        sectors = self.xtdata.get_sector_list("concept")
                    except TypeError:
                        # 如果不支持参数，获取所有板块
                        sectors = self.xtdata.get_sector_list()
            else:
                # 获取所有板块
                sectors = self.xtdata.get_sector_list()

            if not sectors:
                logger.warning(f"未获取到{sector_type}板块数据")
                return []

            # 格式化板块数据
            formatted_sectors = []
            for sector in sectors:
                try:
                    sector_info = {
                        'code': sector,
                        'name': sector,
                        'type': sector_type,
                        'stock_count': 0,
                        'change_percent': 0
                    }

                    # 获取板块内股票列表
                    sector_stocks = self.xtdata.get_stock_list_in_sector(sector)
                    if sector_stocks:
                        sector_info['stock_count'] = len(sector_stocks)

                    formatted_sectors.append(sector_info)

                except Exception as e:
                    logger.warning(f"处理板块 {sector} 失败: {e}")

            logger.info(f"成功获取 {len(formatted_sectors)} 个{sector_type}板块")
            return formatted_sectors

        except Exception as e:
            logger.error(f"获取板块数据失败: {e}")
            raise DataSourceError(f"获取板块数据失败: {e}")
    
    def get_financial_data(self, symbol: str, report_type: str = "annual") -> Dict[str, Any]:
        """
        获取财务数据
        
        Args:
            symbol: 股票代码
            report_type: 报告类型
            
        Returns:
            Dict[str, Any]: 财务数据
        """
        try:
            if not self._is_connected:
                raise ConnectionError("XtData未连接")

            logger.info(f"获取 {symbol} 的财务数据")
            
            # 获取财务数据
            financial_data = self.xtdata.get_financial_data(
                stock_list=[symbol],
                table_list=['BalanceSheet', 'Income', 'CashFlow'],
                start_time='',
                end_time='',
                report_type=report_type
            )
            
            if not financial_data or symbol not in financial_data:
                raise DataSourceError(f"未获取到 {symbol} 的财务数据")
            
            return financial_data[symbol]
            
        except Exception as e:
            logger.error(f"获取 {symbol} 财务数据失败: {e}")
            raise DataSourceError(f"获取财务数据失败: {e}")
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self.stock_list_cache or not self.cache_timestamp:
            return False
        
        return (time.time() - self.cache_timestamp) < self.cache_duration
    
    def _rate_limit(self):
        """速率限制"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < (1.0 / self.max_requests_per_second):
            sleep_time = (1.0 / self.max_requests_per_second) - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()

    def _validate_date_format(self, date_str: str) -> bool:
        """
        验证日期格式

        Args:
            date_str: 日期字符串

        Returns:
            bool: 格式是否正确
        """
        try:
            # 支持多种日期格式
            formats = ['%Y%m%d', '%Y-%m-%d', '%Y/%m/%d']
            for fmt in formats:
                try:
                    datetime.strptime(date_str, fmt)
                    return True
                except ValueError:
                    continue
            return False
        except Exception:
            return False

    def _convert_date_format(self, date_str: str) -> str:
        """
        转换日期格式为XtData需要的YYYYMMDD格式

        Args:
            date_str: 输入日期字符串

        Returns:
            str: YYYYMMDD格式的日期字符串
        """
        try:
            # 支持多种输入格式
            formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d']

            for fmt in formats:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%Y%m%d')
                except ValueError:
                    continue

            # 如果都不匹配，返回原字符串
            logger.warning(f"无法解析日期格式: {date_str}")
            return date_str

        except Exception as e:
            logger.error(f"日期格式转换失败: {e}")
            return date_str

    def _should_use_mock_data(self) -> bool:
        """
        判断是否应该使用模拟数据

        Returns:
            bool: 是否使用模拟数据
        """
        # 检查环境变量或配置
        import os
        return os.getenv('USE_MOCK_DATA', 'false').lower() == 'true'

    def _setup_mock_data(self) -> bool:
        """
        设置模拟数据模式

        Returns:
            bool: 设置是否成功
        """
        try:
            from mock_xtdata import MockXtData
            self.xtdata = MockXtData()
            self._is_connected = True
            logger.info("模拟数据模式设置成功")
            return True
        except ImportError:
            logger.error("无法导入模拟数据模块")
            return False
        except Exception as e:
            logger.error(f"设置模拟数据模式失败: {e}")
            return False

    def _get_realtime_as_history(self, symbol: str):
        """
        获取实时数据并转换为历史数据格式

        Args:
            symbol: 股票代码

        Returns:
            pandas.DataFrame: 转换后的历史数据格式
        """
        try:
            # 获取实时行情
            quotes = self.xtdata.get_full_tick([symbol])

            if not quotes or symbol not in quotes:
                logger.warning(f"无法获取 {symbol} 的实时数据")
                return None

            quote = quotes[symbol]

            # 转换为历史数据格式
            import pandas as pd

            current_time = datetime.now()
            current_date = current_time.strftime('%Y%m%d')

            data = {
                'time': [current_time],
                'trade_date': [current_date],  # 添加必要的trade_date列
                'open': [quote.get('open', quote.get('lastPrice', 0))],
                'high': [quote.get('high', quote.get('lastPrice', 0))],
                'low': [quote.get('low', quote.get('lastPrice', 0))],
                'close': [quote.get('lastPrice', 0)],
                'volume': [quote.get('volume', 0)],
                'amount': [quote.get('amount', 0)],
                'settelementPrice': [quote.get('settlementPrice', 0)],
                'openInterest': [quote.get('openInt', 0)],
                'preClose': [quote.get('lastClose', 0)],
                'suspendFlag': [0]
            }

            df = pd.DataFrame(data)
            df.set_index('time', inplace=True)

            logger.info(f"成功将 {symbol} 实时数据转换为历史数据格式")
            return df

        except Exception as e:
            logger.error(f"转换 {symbol} 实时数据失败: {e}")
            return None

    def test_connection(self) -> bool:
        """
        测试连接状态

        Returns:
            bool: 连接是否正常
        """
        try:
            if not self._is_connected:
                return False

            # 测试基本功能
            if self.xtdata:
                # 尝试获取一个简单的数据来验证连接
                test_symbols = ['000001.SZ']  # 平安银行
                try:
                    data = self.xtdata.get_market_data(
                        stock_list=test_symbols,
                        period='1d',
                        count=1
                    )
                    return data is not None and len(data) > 0
                except Exception as e:
                    logger.warning(f"连接测试失败: {e}")
                    return False

            return False

        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            return False

    def get_sector_list(self, sector_type: str = "industry") -> List[Dict[str, str]]:
        """
        获取板块列表

        Args:
            sector_type: 板块类型 ("industry", "concept", "region")

        Returns:
            List[Dict[str, str]]: 板块信息列表

        Raises:
            DataSourceError: 数据获取失败
        """
        try:
            if not self._is_connected:
                raise ConnectionError("XtData未连接")

            # 速率限制
            self._rate_limit()

            logger.info(f"获取板块列表，类型: {sector_type}")

            # 根据板块类型获取数据
            if sector_type == "industry":
                # 获取行业板块
                if hasattr(self.xtdata, 'get_industry_list'):
                    sectors = self.xtdata.get_industry_list()
                else:
                    # 检查get_sector_list是否支持参数
                    try:
                        sectors = self.xtdata.get_sector_list("industry")
                    except TypeError:
                        # 如果不支持参数，获取所有板块
                        sectors = self.xtdata.get_sector_list()
            elif sector_type == "concept":
                # 获取概念板块
                if hasattr(self.xtdata, 'get_concept_list'):
                    sectors = self.xtdata.get_concept_list()
                else:
                    # 检查get_sector_list是否支持参数
                    try:
                        sectors = self.xtdata.get_sector_list("concept")
                    except TypeError:
                        # 如果不支持参数，获取所有板块
                        sectors = self.xtdata.get_sector_list()
            else:
                # 获取所有板块
                sectors = self.xtdata.get_sector_list()

            if not sectors:
                logger.warning(f"未获取到{sector_type}板块数据")
                return []

            # 转换为标准格式
            result = []
            for sector in sectors:
                if isinstance(sector, dict):
                    result.append({
                        'code': sector.get('code', ''),
                        'name': sector.get('name', ''),
                        'type': sector_type
                    })
                elif isinstance(sector, str):
                    # 如果是字符串，假设是板块名称
                    result.append({
                        'code': sector,
                        'name': sector,
                        'type': sector_type
                    })

            logger.info(f"成功获取 {len(result)} 个{sector_type}板块")
            return result

        except Exception as e:
            logger.error(f"获取板块列表失败: {e}")
            raise DataSourceError(f"获取板块列表失败: {e}")

    def get_sector_constituents(self, sector_code: str) -> List[str]:
        """
        获取板块成分股

        Args:
            sector_code: 板块代码

        Returns:
            List[str]: 成分股代码列表

        Raises:
            DataSourceError: 数据获取失败
        """
        try:
            if not self._is_connected:
                raise ConnectionError("XtData未连接")

            # 速率限制
            self._rate_limit()

            logger.info(f"获取板块 {sector_code} 的成分股")

            # 获取板块成分股
            constituents = self.xtdata.get_sector_stocks(sector_code)

            if not constituents:
                logger.warning(f"未获取到板块 {sector_code} 的成分股")
                return []

            # 确保返回的是股票代码列表
            if isinstance(constituents, list):
                result = [str(stock) for stock in constituents]
            else:
                result = []

            logger.info(f"成功获取板块 {sector_code} 的 {len(result)} 只成分股")
            return result

        except Exception as e:
            logger.error(f"获取板块成分股失败: {e}")
            raise DataSourceError(f"获取板块成分股失败: {e}")

    def get_trading_calendar(self, start_date: str, end_date: str) -> List[str]:
        """
        获取交易日历

        Args:
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)

        Returns:
            List[str]: 交易日列表 (YYYYMMDD格式)

        Raises:
            DataSourceError: 数据获取失败
        """
        try:
            if not self._is_connected:
                raise ConnectionError("XtData未连接")

            # 速率限制
            self._rate_limit()

            logger.info(f"获取交易日历: {start_date} - {end_date}")

            # 获取交易日历
            trading_days = self.xtdata.get_trading_dates(
                market='SH',  # 使用上海市场
                start_time=start_date,
                end_time=end_date
            )

            if not trading_days:
                logger.warning(f"未获取到交易日历数据: {start_date} - {end_date}")
                return []

            # 转换为字符串格式
            result = []
            for day in trading_days:
                if isinstance(day, str):
                    result.append(day)
                elif hasattr(day, 'strftime'):
                    result.append(day.strftime('%Y%m%d'))
                else:
                    result.append(str(day))

            logger.info(f"成功获取 {len(result)} 个交易日")
            return result

        except Exception as e:
            logger.error(f"获取交易日历失败: {e}")
            raise DataSourceError(f"获取交易日历失败: {e}")

    def download_history_data(
        self,
        symbols: List[str],
        period: str = "1d",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        callback: Optional[callable] = None
    ) -> bool:
        """
        批量下载历史数据

        Args:
            symbols: 股票代码列表
            period: 数据周期
            start_date: 开始日期
            end_date: 结束日期
            callback: 进度回调函数

        Returns:
            bool: 下载是否成功

        Raises:
            DataSourceError: 下载失败
        """
        try:
            if not self._is_connected:
                raise ConnectionError("XtData未连接")

            if not symbols:
                logger.warning("股票代码列表为空")
                return True

            logger.info(f"开始批量下载 {len(symbols)} 只股票的历史数据")

            # 设置默认日期范围
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')

            # 转换周期格式
            period_map = {
                '1d': '1d',
                '1m': '1m',
                '5m': '5m',
                '15m': '15m',
                '30m': '30m',
                '60m': '1h',
                '1h': '1h'
            }
            xt_period = period_map.get(period, '1d')

            success_count = 0
            total_count = len(symbols)

            # 批量下载
            for i, symbol in enumerate(symbols):
                try:
                    # 下载单个股票数据
                    result = self.xtdata.download_history_data(
                        stock_code=symbol,
                        period=xt_period,
                        start_time=start_date,
                        end_time=end_date
                    )

                    if result == 0:  # 0表示成功
                        success_count += 1
                        logger.debug(f"成功下载 {symbol} 的历史数据")
                    else:
                        logger.warning(f"下载 {symbol} 的历史数据失败，错误代码: {result}")

                    # 进度回调
                    if callback:
                        progress = (i + 1) / total_count * 100
                        callback(progress, f"已下载 {i + 1}/{total_count} 只股票")

                    # 避免请求过快
                    time.sleep(0.1)

                except Exception as e:
                    logger.error(f"下载 {symbol} 的历史数据失败: {e}")
                    continue

            logger.info(f"批量下载完成，成功: {success_count}/{total_count}")
            return success_count > 0

        except Exception as e:
            logger.error(f"批量下载历史数据失败: {e}")
            raise DataSourceError(f"批量下载历史数据失败: {e}")

    def is_connected_status(self) -> bool:
        """检查连接状态"""
        return self._is_connected

    def get_status(self) -> Dict[str, Any]:
        """获取适配器状态"""
        return {
            'connected': self._is_connected,
            'host': self.host,
            'port': self.port,
            'cache_valid': self._is_cache_valid(),
            'cached_stocks': len(self.stock_list_cache) if self.stock_list_cache else 0,
            'last_cache_update': self.cache_timestamp
        }