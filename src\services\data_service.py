"""
数据服务层

整合威科夫分析引擎、相对强弱引擎、选股引擎，
为UI层提供统一的数据接口
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor

from ..engines.wyckoff import WyckoffAnalysisEngine, MarketStructure, WyckoffSignal
from ..engines.relative_strength import RelativeStrengthEngine, RSResult, RSAnalysis
from ..engines.selection import StockSelectionEngine, SelectionResult, StrategyConfig
from ..utils.logger import get_logger
from ..utils.exceptions import DataSourceError, CalculationError
from ..utils.cache import CacheManager
from ..database.database_manager import DatabaseManager
from ..data_sources.manager import DataSourceManager
from .data_download_service import DataDownloadService, DownloadProgress

logger = get_logger(__name__)


@dataclass
class StockInfo:
    """股票基本信息"""
    symbol: str
    name: str
    sector: str
    market_cap: float
    price: float
    change: float
    change_percent: float
    volume: int
    last_update: datetime


@dataclass
class AnalysisResult:
    """综合分析结果"""
    stock_info: StockInfo
    wyckoff_analysis: Optional[MarketStructure]
    wyckoff_signals: List[WyckoffSignal]
    rs_analysis: Optional[RSAnalysis]
    rs_ranking: Optional[RSResult]
    analysis_time: datetime


class DataService:
    """数据服务类"""

    def __init__(self, data_source_manager: Optional[DataSourceManager] = None, use_real_data: bool = True):
        """
        初始化数据服务

        Args:
            data_source_manager: 数据源管理器
            use_real_data: 是否使用真实数据
        """
        # 初始化分析引擎
        self.wyckoff_engine = WyckoffAnalysisEngine()
        self.rs_engine = RelativeStrengthEngine()
        self.selection_engine = StockSelectionEngine()

        # 缓存管理器
        self.cache_manager = CacheManager()

        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4)

        # 数据管理
        self.use_real_data = use_real_data
        self.data_source_manager = data_source_manager
        self.db_manager = DatabaseManager() if use_real_data else None
        self.download_service = None

        if use_real_data and data_source_manager:
            self.download_service = DataDownloadService(data_source_manager, self.db_manager)

        # 数据缓存
        self.stocks_data: Dict[str, pd.DataFrame] = {}
        self.benchmark_data: Optional[pd.DataFrame] = None
        self.stock_list: List[StockInfo] = []

        logger.info(f"数据服务初始化完成 (使用{'真实' if use_real_data else '模拟'}数据)")
    
    def create_sample_data(self) -> Dict[str, pd.DataFrame]:
        """创建示例数据用于演示"""
        np.random.seed(42)
        
        # 创建日期范围
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
        dates = dates[dates.weekday < 5]  # 只保留工作日
        
        stocks_data = {}
        
        # 示例股票列表
        stock_symbols = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '002594.SZ',
            '600000.SH', '600036.SH', '600519.SH', '600887.SH', '601318.SH'
        ]
        
        for symbol in stock_symbols:
            # 生成价格数据
            base_price = np.random.uniform(10, 100)
            returns = np.random.normal(0.001, 0.02, len(dates))
            prices = [base_price]
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            # 生成成交量数据
            base_volume = np.random.randint(1000000, 10000000)
            volumes = np.random.lognormal(np.log(base_volume), 0.5, len(dates))
            
            # 创建OHLC数据
            data = pd.DataFrame({
                'date': dates,
                'open': [p * np.random.uniform(0.98, 1.02) for p in prices],
                'high': [p * np.random.uniform(1.00, 1.05) for p in prices],
                'low': [p * np.random.uniform(0.95, 1.00) for p in prices],
                'close': prices,
                'volume': volumes.astype(int)
            })
            
            data.set_index('date', inplace=True)
            stocks_data[symbol] = data
        
        return stocks_data
    
    def load_stock_list(self) -> List[StockInfo]:
        """加载股票列表"""
        try:
            if self.use_real_data and self.db_manager:
                # 从数据库加载真实股票数据
                return self._load_real_stock_list()
            else:
                # 使用模拟数据
                return self._load_sample_stock_list()

        except Exception as e:
            logger.error(f"加载股票列表失败: {e}")
            raise DataSourceError(f"加载股票列表失败: {e}")

    def _load_real_stock_list(self) -> List[StockInfo]:
        """从数据库加载真实股票列表（仅基本信息，不获取实时价格）"""
        try:
            stock_records = self.db_manager.get_stock_list()
            stock_list = []

            for record in stock_records:
                # 只加载基本信息，不获取实时价格（避免启动时大量API调用）
                stock_info = StockInfo(
                    symbol=record['stock_code'],
                    name=record['stock_name'],
                    sector=record.get('industry', '未知'),
                    market_cap=record.get('market_cap', 0.0),
                    price=0.0,  # 启动时不获取实时价格
                    change=0.0,
                    change_percent=0.0,
                    volume=0,
                    last_update=datetime.now()
                )
                stock_list.append(stock_info)

            self.stock_list = stock_list
            logger.info(f"从数据库加载股票列表完成，共{len(stock_list)}只股票（不含实时价格）")
            return stock_list

        except Exception as e:
            logger.error(f"从数据库加载股票列表失败: {e}")
            # 如果数据库加载失败，返回示例数据
            return self._load_sample_stock_list()

    def _load_sample_stock_list(self) -> List[StockInfo]:
        """加载示例股票列表"""
        sample_stocks = [
            StockInfo(
                symbol='000001.SZ',
                name='平安银行',
                sector='银行',
                market_cap=2500.0,
                price=12.50,
                change=0.15,
                change_percent=1.22,
                volume=15000000,
                last_update=datetime.now()
            ),
            StockInfo(
                symbol='000002.SZ',
                name='万科A',
                sector='房地产',
                market_cap=1800.0,
                price=16.80,
                change=-0.25,
                change_percent=-1.47,
                volume=8500000,
                last_update=datetime.now()
            ),
            StockInfo(
                symbol='600519.SH',
                name='贵州茅台',
                sector='食品饮料',
                market_cap=25000.0,
                price=2050.0,
                change=25.0,
                change_percent=1.23,
                volume=1200000,
                last_update=datetime.now()
            ),
            StockInfo(
                symbol='000858.SZ',
                name='五粮液',
                sector='食品饮料',
                market_cap=8500.0,
                price=220.0,
                change=3.5,
                change_percent=1.61,
                volume=2800000,
                last_update=datetime.now()
            ),
            StockInfo(
                symbol='601318.SH',
                name='中国平安',
                sector='保险',
                market_cap=12000.0,
                price=65.5,
                change=-1.2,
                change_percent=-1.80,
                volume=18000000,
                last_update=datetime.now()
            )
        ]

        self.stock_list = sample_stocks
        logger.info(f"加载示例股票列表完成，共{len(sample_stocks)}只股票")
        return sample_stocks

    def _load_from_data_source(self) -> List[StockInfo]:
        """从数据源直接加载股票列表（仅基本信息，避免大量API调用）"""
        try:
            if not self.data_source_manager:
                return self._load_sample_stock_list()

            data_source = self.data_source_manager.get_best_source()
            if not data_source:
                return self._load_sample_stock_list()

            # 获取股票列表
            stock_codes = data_source.get_stock_list()
            if not stock_codes:
                return self._load_sample_stock_list()

            stock_list = []
            # 限制数量以避免界面卡顿，并且不获取实时价格
            limited_codes = stock_codes[:100] if len(stock_codes) > 100 else stock_codes

            for stock_code in limited_codes:
                # 获取股票基本信息和名称
                stock_name = self._get_stock_name(stock_code)
                sector = self._get_stock_sector(stock_code)

                # 创建股票信息对象，启动时不获取实时价格以提高启动速度
                stock_info = StockInfo(
                    symbol=stock_code,
                    name=stock_name,
                    sector=sector,
                    market_cap=0.0,
                    price=0.0,  # 启动时不获取实时价格
                    change=0.0,
                    change_percent=0.0,
                    volume=0,
                    last_update=datetime.now()
                )
                stock_list.append(stock_info)

            if stock_list:
                self.stock_list = stock_list
                logger.info(f"从数据源加载股票列表完成，共{len(stock_list)}只股票（不含实时价格）")
                return stock_list
            else:
                return self._load_sample_stock_list()

        except Exception as e:
            logger.error(f"从数据源加载股票列表失败: {e}")
            return self._load_sample_stock_list()

    def _get_stock_name(self, stock_code: str) -> str:
        """获取股票名称"""
        try:
            if self.data_source_manager:
                data_source = self.data_source_manager.get_best_source()
                if data_source and hasattr(data_source, '_get_stock_name'):
                    return data_source._get_stock_name(stock_code)

            # 如果无法获取名称，返回代码
            return stock_code.split('.')[0]
        except Exception as e:
            logger.warning(f"获取股票名称失败 {stock_code}: {e}")
            return stock_code.split('.')[0]

    def _get_stock_sector(self, stock_code: str) -> str:
        """获取股票板块"""
        try:
            # 根据股票代码判断基本板块
            if stock_code.endswith('.SH'):
                if stock_code.startswith('60'):
                    return '沪市主板'
                elif stock_code.startswith('688'):
                    return '科创板'
                else:
                    return '沪市其他'
            elif stock_code.endswith('.SZ'):
                if stock_code.startswith('00'):
                    return '深市主板'
                elif stock_code.startswith('30'):
                    return '创业板'
                elif stock_code.startswith('002'):
                    return '中小板'
                else:
                    return '深市其他'
            else:
                return '其他市场'
        except Exception as e:
            logger.warning(f"获取股票板块失败 {stock_code}: {e}")
            return '未知'

    def _get_latest_quote(self, stock_code: str) -> Dict[str, Any]:
        """获取最新行情（仅从数据库获取，避免API调用）"""
        try:
            if self.db_manager:
                # 从数据库获取最新行情
                df = self.db_manager.get_stock_quotes(stock_code, limit=2)
                if not df.empty:
                    latest = df.iloc[0]
                    prev = df.iloc[1] if len(df) > 1 else latest

                    change = latest['close'] - prev['close']
                    change_percent = (change / prev['close']) * 100 if prev['close'] != 0 else 0

                    return {
                        'close': latest['close'],
                        'change': change,
                        'change_percent': change_percent,
                        'volume': latest['volume']
                    }

            # 如果数据库没有数据，返回默认值（不调用API）
            return {'close': 0.0, 'change': 0.0, 'change_percent': 0.0, 'volume': 0}

        except Exception as e:
            logger.warning(f"获取最新行情失败 {stock_code}: {e}")
            return {'close': 0.0, 'change': 0.0, 'change_percent': 0.0, 'volume': 0}

    async def update_real_time_quotes(self, stock_codes: List[str]) -> Dict[str, Dict[str, Any]]:
        """异步更新实时行情（批量获取）"""
        try:
            if not self.data_source_manager:
                return {}

            data_source = self.data_source_manager.get_best_source()
            if not data_source:
                return {}

            # 批量获取实时行情
            quotes = {}
            batch_size = 50  # 每批处理50只股票

            for i in range(0, len(stock_codes), batch_size):
                batch_codes = stock_codes[i:i + batch_size]

                try:
                    # 使用批量API获取数据
                    market_data = data_source.get_market_data(
                        symbols=batch_codes,
                        period='1d'
                    )

                    if market_data:
                        for code in batch_codes:
                            if hasattr(market_data, 'data') and not market_data.data.empty:
                                latest_data = market_data.data.iloc[-1]
                                quotes[code] = {
                                    'close': float(latest_data.get('close', 0.0)),
                                    'change': 0.0,  # 需要计算
                                    'change_percent': 0.0,  # 需要计算
                                    'volume': int(latest_data.get('volume', 0))
                                }

                except Exception as e:
                    logger.warning(f"批量获取行情失败 {batch_codes}: {e}")
                    continue

            return quotes

        except Exception as e:
            logger.error(f"更新实时行情失败: {e}")
            return {}

    def update_stock_real_time_data(self, stock_codes: List[str] = None) -> bool:
        """更新股票实时数据"""
        try:
            if not stock_codes and self.stock_list:
                stock_codes = [stock.symbol for stock in self.stock_list[:50]]  # 限制数量

            if not stock_codes:
                return False

            # 获取实时行情数据
            if self.data_source_manager:
                data_source = self.data_source_manager.get_best_source()
                if data_source and hasattr(data_source, 'xtdata'):
                    try:
                        # 使用XtData获取实时行情
                        for stock_code in stock_codes:
                            try:
                                # 获取最新tick数据
                                tick_data = data_source.xtdata.get_full_tick([stock_code])
                                if tick_data and stock_code in tick_data:
                                    tick = tick_data[stock_code]
                                    if tick is not None and len(tick) > 0:
                                        latest_tick = tick[-1] if hasattr(tick, '__len__') else tick

                                        # 更新股票列表中的数据
                                        for stock in self.stock_list:
                                            if stock.symbol == stock_code:
                                                if hasattr(latest_tick, 'lastPrice'):
                                                    stock.price = float(latest_tick.lastPrice)
                                                elif hasattr(latest_tick, 'close'):
                                                    stock.price = float(latest_tick.close)

                                                if hasattr(latest_tick, 'lastClose') and latest_tick.lastClose > 0:
                                                    prev_close = float(latest_tick.lastClose)
                                                    if stock.price > 0:
                                                        stock.change = stock.price - prev_close
                                                        stock.change_percent = (stock.change / prev_close) * 100

                                                if hasattr(latest_tick, 'volume'):
                                                    stock.volume = int(latest_tick.volume)

                                                stock.last_update = datetime.now()
                                                break
                            except Exception as e:
                                logger.warning(f"更新股票 {stock_code} 实时数据失败: {e}")
                                continue

                        logger.info(f"成功更新 {len(stock_codes)} 只股票的实时数据")
                        return True
                    except Exception as e:
                        logger.error(f"批量更新实时数据失败: {e}")
                        return False

            return False
        except Exception as e:
            logger.error(f"更新股票实时数据失败: {e}")
            return False
    
    def get_stock_list(self) -> List[Dict]:
        """获取股票列表"""
        try:
            # 使用数据源管理器获取股票列表
            sources = self.data_source_manager.get_available_sources()
            if sources:
                # 使用第一个可用的数据源
                source = sources[0]
                return source.get_stock_list()
            else:
                logger.warning("没有可用的数据源")
                return []
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    def get_stock_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取股票数据"""
        try:
            # 先检查缓存
            if symbol in self.stocks_data:
                return self.stocks_data[symbol]

            # 从数据库或数据源获取数据
            if self.use_real_data and self.db_manager:
                df = self.db_manager.get_stock_quotes(symbol, limit=500)  # 获取最近500天数据
                if not df.empty:
                    self.stocks_data[symbol] = df
                    return df

            # 如果没有真实数据，尝试从数据源获取
            if self.data_source_manager:
                data_source = self.data_source_manager.get_best_source()
                if data_source:
                    try:
                        # 获取历史数据
                        end_date = datetime.now().strftime('%Y%m%d')
                        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')

                        market_data = data_source.get_market_data(
                            symbols=[symbol],
                            period='1d',
                            start_date=start_date,
                            end_date=end_date
                        )

                        if market_data and len(market_data) > 0:
                            # 转换为DataFrame格式
                            df = self._convert_market_data_to_df(market_data)
                            if not df.empty:
                                self.stocks_data[symbol] = df
                                return df
                    except Exception as e:
                        logger.warning(f"从数据源获取股票数据失败 {symbol}: {e}")

            # 如果都失败了，使用示例数据
            if symbol not in self.stocks_data:
                sample_data = self.create_sample_data()
                self.stocks_data.update(sample_data)

            return self.stocks_data.get(symbol)

        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {e}")
            return None

    def _convert_market_data_to_df(self, market_data: Any) -> pd.DataFrame:
        """将市场数据转换为DataFrame格式"""
        try:
            if isinstance(market_data, pd.DataFrame):
                return market_data
            elif isinstance(market_data, list) and len(market_data) > 0:
                df = pd.DataFrame(market_data)

                # 标准化列名
                column_mapping = {
                    'date': 'date',
                    'open': 'open',
                    'high': 'high',
                    'low': 'low',
                    'close': 'close',
                    'volume': 'volume'
                }

                df = df.rename(columns=column_mapping)

                # 处理日期索引
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)

                return df
            else:
                return pd.DataFrame()

        except Exception as e:
            logger.warning(f"转换市场数据格式失败: {e}")
            return pd.DataFrame()
    
    def analyze_stock(self, symbol: str) -> Optional[AnalysisResult]:
        """分析单只股票"""
        try:
            # 获取股票数据
            stock_data = self.get_stock_data(symbol)
            if stock_data is None or stock_data.empty:
                raise DataSourceError(f"无法获取股票数据: {symbol}")
            
            # 获取股票基本信息
            stock_info = next((s for s in self.stock_list if s.symbol == symbol), None)
            if stock_info is None:
                # 创建默认股票信息
                stock_info = StockInfo(
                    symbol=symbol,
                    name=symbol,
                    sector='未知',
                    market_cap=0.0,
                    price=float(stock_data['close'].iloc[-1]),
                    change=0.0,
                    change_percent=0.0,
                    volume=int(stock_data['volume'].iloc[-1]),
                    last_update=datetime.now()
                )
            
            # 威科夫分析
            wyckoff_analysis = None
            wyckoff_signals = []
            try:
                wyckoff_analysis = self.wyckoff_engine.analyze_market_structure(stock_data)
                wyckoff_signals = self.wyckoff_engine.detect_wyckoff_signals(stock_data)
            except Exception as e:
                logger.warning(f"威科夫分析失败 {symbol}: {e}")
            
            # 相对强弱分析
            rs_analysis = None
            rs_ranking = None
            try:
                if self.benchmark_data is None:
                    # 创建基准数据
                    self.benchmark_data = self._create_benchmark_data()
                
                rs_analysis = self.rs_engine.analyze_relative_strength(
                    {'symbol': symbol}, self.benchmark_data
                )
                
                # 简化的RS排名
                rs_value = self.rs_engine.calculate_rs_value(stock_data, self.benchmark_data)
                rs_ranking = RSResult(
                    symbol=symbol,
                    rs_value=rs_value,
                    rs_rank=0,
                    rs_percentile=50.0,
                    benchmark="market_index",
                    timeframe=self.rs_engine.TimeFrame.DAILY,
                    calculation_date=pd.Timestamp.now(),
                    momentum_score=0.5,
                    trend_strength=0.5
                )
            except Exception as e:
                logger.warning(f"相对强弱分析失败 {symbol}: {e}")
            
            result = AnalysisResult(
                stock_info=stock_info,
                wyckoff_analysis=wyckoff_analysis,
                wyckoff_signals=wyckoff_signals,
                rs_analysis=rs_analysis,
                rs_ranking=rs_ranking,
                analysis_time=datetime.now()
            )
            
            logger.info(f"股票分析完成: {symbol}")
            return result
            
        except Exception as e:
            logger.error(f"股票分析失败 {symbol}: {e}")
            return None
    
    def _create_benchmark_data(self) -> pd.DataFrame:
        """创建基准数据"""
        # 创建简单的基准指数数据
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
        dates = dates[dates.weekday < 5]
        
        np.random.seed(123)
        base_price = 3000
        returns = np.random.normal(0.0005, 0.015, len(dates))
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        benchmark_data = pd.DataFrame({
            'date': dates,
            'open': [p * np.random.uniform(0.995, 1.005) for p in prices],
            'high': [p * np.random.uniform(1.000, 1.02) for p in prices],
            'low': [p * np.random.uniform(0.98, 1.000) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000000, 2000000000, len(dates))
        })
        
        benchmark_data.set_index('date', inplace=True)
        return benchmark_data
    
    def refresh_data(self) -> bool:
        """刷新数据"""
        try:
            logger.info("开始刷新数据...")

            # 重新加载股票列表
            self.load_stock_list()

            # 清除缓存
            self.stocks_data.clear()
            self.benchmark_data = None

            if self.use_real_data:
                # 真实数据模式：清除缓存，下次获取时会从数据库重新加载
                logger.info("真实数据刷新完成")
            else:
                # 示例数据模式：重新创建示例数据
                sample_data = self.create_sample_data()
                self.stocks_data.update(sample_data)
                logger.info("示例数据刷新完成")

            return True

        except Exception as e:
            logger.error(f"数据刷新失败: {e}")
            return False

    def get_sector_list(self, sector_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取板块列表

        Args:
            sector_type: 板块类型，None表示获取所有板块，'industry'表示行业板块，'concept'表示概念板块

        Returns:
            List[Dict[str, Any]]: 板块信息列表
        """
        try:
            if self.use_real_data and self.data_source_manager:
                # 真实数据模式：从数据源获取
                data_source = self.data_source_manager.get_best_source()
                if data_source and hasattr(data_source, 'get_sector_list'):
                    if sector_type:
                        return data_source.get_sector_list(sector_type)
                    else:
                        # 获取所有类型的板块
                        industry_sectors = data_source.get_sector_list("industry")
                        concept_sectors = data_source.get_sector_list("concept")
                        return industry_sectors + concept_sectors
                else:
                    logger.warning("数据源不支持板块数据获取，使用示例数据")
                    return self._get_sample_sector_list(sector_type)
            else:
                # 示例数据模式
                return self._get_sample_sector_list(sector_type)

        except Exception as e:
            logger.error(f"获取板块列表失败: {e}")
            return self._get_sample_sector_list(sector_type)

    def _get_sample_sector_list(self, sector_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取示例板块列表"""
        # 示例行业板块
        industry_sectors = [
            {"code": "银行", "name": "银行", "type": "industry"},
            {"code": "非银金融", "name": "非银金融", "type": "industry"},
            {"code": "房地产", "name": "房地产", "type": "industry"},
            {"code": "建筑装饰", "name": "建筑装饰", "type": "industry"},
            {"code": "建筑材料", "name": "建筑材料", "type": "industry"},
            {"code": "钢铁", "name": "钢铁", "type": "industry"},
            {"code": "有色金属", "name": "有色金属", "type": "industry"},
            {"code": "煤炭", "name": "煤炭", "type": "industry"},
            {"code": "石油石化", "name": "石油石化", "type": "industry"},
            {"code": "化工", "name": "化工", "type": "industry"},
            {"code": "基础化工", "name": "基础化工", "type": "industry"},
            {"code": "医药生物", "name": "医药生物", "type": "industry"},
            {"code": "食品饮料", "name": "食品饮料", "type": "industry"},
            {"code": "农林牧渔", "name": "农林牧渔", "type": "industry"},
            {"code": "纺织服装", "name": "纺织服装", "type": "industry"},
            {"code": "轻工制造", "name": "轻工制造", "type": "industry"},
            {"code": "商贸零售", "name": "商贸零售", "type": "industry"},
            {"code": "交通运输", "name": "交通运输", "type": "industry"},
            {"code": "汽车", "name": "汽车", "type": "industry"},
            {"code": "家用电器", "name": "家用电器", "type": "industry"},
            {"code": "电力设备", "name": "电力设备", "type": "industry"},
            {"code": "机械设备", "name": "机械设备", "type": "industry"},
            {"code": "国防军工", "name": "国防军工", "type": "industry"},
            {"code": "电子", "name": "电子", "type": "industry"},
            {"code": "计算机", "name": "计算机", "type": "industry"},
            {"code": "通信", "name": "通信", "type": "industry"},
            {"code": "传媒", "name": "传媒", "type": "industry"},
            {"code": "电力公用事业", "name": "电力公用事业", "type": "industry"},
            {"code": "环保", "name": "环保", "type": "industry"},
            {"code": "美容护理", "name": "美容护理", "type": "industry"},
            {"code": "社会服务", "name": "社会服务", "type": "industry"},
            {"code": "综合", "name": "综合", "type": "industry"}
        ]

        # 示例概念板块
        concept_sectors = [
            {"code": "新能源", "name": "新能源", "type": "concept"},
            {"code": "5G", "name": "5G", "type": "concept"},
            {"code": "人工智能", "name": "人工智能", "type": "concept"},
            {"code": "区块链", "name": "区块链", "type": "concept"},
            {"code": "物联网", "name": "物联网", "type": "concept"},
            {"code": "芯片", "name": "芯片", "type": "concept"},
            {"code": "半导体", "name": "半导体", "type": "concept"},
            {"code": "新基建", "name": "新基建", "type": "concept"},
            {"code": "碳中和", "name": "碳中和", "type": "concept"},
            {"code": "数字货币", "name": "数字货币", "type": "concept"},
            {"code": "工业互联网", "name": "工业互联网", "type": "concept"},
            {"code": "云计算", "name": "云计算", "type": "concept"},
            {"code": "大数据", "name": "大数据", "type": "concept"},
            {"code": "虚拟现实", "name": "虚拟现实", "type": "concept"},
            {"code": "增强现实", "name": "增强现实", "type": "concept"},
            {"code": "自动驾驶", "name": "自动驾驶", "type": "concept"},
            {"code": "新能源汽车", "name": "新能源汽车", "type": "concept"},
            {"code": "锂电池", "name": "锂电池", "type": "concept"},
            {"code": "光伏", "name": "光伏", "type": "concept"},
            {"code": "风电", "name": "风电", "type": "concept"},
            {"code": "储能", "name": "储能", "type": "concept"},
            {"code": "氢能源", "name": "氢能源", "type": "concept"},
            {"code": "核电", "name": "核电", "type": "concept"},
            {"code": "特高压", "name": "特高压", "type": "concept"},
            {"code": "充电桩", "name": "充电桩", "type": "concept"},
            {"code": "工业4.0", "name": "工业4.0", "type": "concept"},
            {"code": "智能制造", "name": "智能制造", "type": "concept"},
            {"code": "机器人", "name": "机器人", "type": "concept"},
            {"code": "无人机", "name": "无人机", "type": "concept"},
            {"code": "3D打印", "name": "3D打印", "type": "concept"},
            {"code": "生物医药", "name": "生物医药", "type": "concept"},
            {"code": "基因测序", "name": "基因测序", "type": "concept"},
            {"code": "细胞治疗", "name": "细胞治疗", "type": "concept"},
            {"code": "医疗器械", "name": "医疗器械", "type": "concept"},
            {"code": "互联网医疗", "name": "互联网医疗", "type": "concept"},
            {"code": "在线教育", "name": "在线教育", "type": "concept"},
            {"code": "直播电商", "name": "直播电商", "type": "concept"},
            {"code": "新零售", "name": "新零售", "type": "concept"},
            {"code": "共享经济", "name": "共享经济", "type": "concept"},
            {"code": "数字经济", "name": "数字经济", "type": "concept"},
            {"code": "网络安全", "name": "网络安全", "type": "concept"},
            {"code": "信息安全", "name": "信息安全", "type": "concept"},
            {"code": "量子通信", "name": "量子通信", "type": "concept"},
            {"code": "卫星导航", "name": "卫星导航", "type": "concept"},
            {"code": "航天航空", "name": "航天航空", "type": "concept"},
            {"code": "海洋经济", "name": "海洋经济", "type": "concept"},
            {"code": "乡村振兴", "name": "乡村振兴", "type": "concept"},
            {"code": "京津冀", "name": "京津冀", "type": "concept"},
            {"code": "长三角", "name": "长三角", "type": "concept"},
            {"code": "粤港澳大湾区", "name": "粤港澳大湾区", "type": "concept"},
            {"code": "一带一路", "name": "一带一路", "type": "concept"},
            {"code": "自贸区", "name": "自贸区", "type": "concept"},
            {"code": "雄安新区", "name": "雄安新区", "type": "concept"},
            {"code": "海南自贸港", "name": "海南自贸港", "type": "concept"},
            {"code": "成渝双城", "name": "成渝双城", "type": "concept"},
            {"code": "RCEP", "name": "RCEP", "type": "concept"},
            {"code": "专精特新", "name": "专精特新", "type": "concept"},
            {"code": "北交所", "name": "北交所", "type": "concept"},
            {"code": "注册制", "name": "注册制", "type": "concept"},
            {"code": "REITs", "name": "REITs", "type": "concept"}
        ]

        if sector_type == "industry":
            return industry_sectors
        elif sector_type == "concept":
            return concept_sectors
        else:
            return industry_sectors + concept_sectors

    async def download_all_stock_data(
        self,
        progress_callback: Optional[Callable[[DownloadProgress], None]] = None,
        include_history: bool = True
    ) -> bool:
        """
        下载所有股票数据

        Args:
            progress_callback: 进度回调函数
            include_history: 是否包含历史数据

        Returns:
            bool: 下载是否成功
        """
        if not self.use_real_data or not self.download_service:
            logger.warning("未启用真实数据模式或下载服务不可用")
            return False

        try:
            success = await self.download_service.download_all_data(
                progress_callback=progress_callback,
                include_history=include_history
            )

            if success:
                # 下载完成后刷新数据
                self.refresh_data()

            return success

        except Exception as e:
            logger.error(f"下载股票数据失败: {e}")
            return False

    def stop_download(self):
        """停止数据下载"""
        if self.download_service:
            self.download_service.stop_download()

    def get_download_progress(self) -> Optional[DownloadProgress]:
        """获取下载进度"""
        if self.download_service:
            return self.download_service.get_progress()
        return None

    def is_downloading(self) -> bool:
        """检查是否正在下载"""
        if self.download_service:
            return self.download_service.is_downloading
        return False

    def get_data_update_info(self) -> Dict[str, Any]:
        """获取数据更新信息"""
        try:
            if not self.use_real_data or not self.db_manager:
                return {
                    'last_update': None,
                    'total_stocks': len(self.stock_list),
                    'data_source': '模拟数据',
                    'need_update': False
                }

            last_update = self.db_manager.get_last_update_time()
            stats = self.db_manager.get_database_stats()

            # 判断是否需要更新（超过1天）
            need_update = True
            if last_update:
                time_diff = datetime.now() - last_update
                need_update = time_diff.total_seconds() > 24 * 3600  # 24小时

            return {
                'last_update': last_update,
                'total_stocks': stats.get('total_stocks', 0),
                'total_quotes': stats.get('total_quotes', 0),
                'latest_trade_date': stats.get('latest_trade_date'),
                'db_size_mb': stats.get('db_size_mb', 0),
                'data_source': '真实数据',
                'need_update': need_update
            }

        except Exception as e:
            logger.error(f"获取数据更新信息失败: {e}")
            return {
                'last_update': None,
                'total_stocks': 0,
                'data_source': '错误',
                'need_update': True
            }
