#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块管理服务
负责板块指数数据的下载、存储、更新和成分股关系管理
"""

import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import pandas as pd

from ..database.manager import DatabaseManager
from ..data_sources.manager import DataSourceManager
from ..utils.logger import get_logger
from ..utils.exceptions import DataSourceError, DatabaseError

logger = get_logger(__name__)


@dataclass
class SectorInfo:
    """板块信息数据类"""
    sector_code: str
    sector_name: str
    sector_type: str  # 'industry' 或 'concept'
    parent_code: Optional[str] = None
    level: int = 1
    is_active: bool = True


@dataclass
class SectorQuote:
    """板块行情数据类"""
    sector_code: str
    trade_date: str
    open: float
    high: float
    low: float
    close: float
    volume: int = 0
    amount: float = 0.0
    change_pct: Optional[float] = None


@dataclass
class SectorConstituent:
    """板块成分股数据类"""
    sector_code: str
    stock_code: str
    stock_name: str
    weight: float = 0.0
    in_date: Optional[str] = None
    out_date: Optional[str] = None


class SectorManager:
    """板块管理器 - 集成真实XtData数据源"""

    def __init__(self, db_manager: DatabaseManager, data_source_manager: DataSourceManager):
        self.db_manager = db_manager
        self.data_source_manager = data_source_manager
        self.is_updating = False

        # 数据质量检查配置
        self.data_quality_config = {
            'min_sector_count': 20,  # 最少板块数量
            'min_stocks_per_sector': 5,  # 每个板块最少股票数
            'max_retry_count': 3,  # 最大重试次数
            'data_freshness_hours': 24  # 数据新鲜度（小时）
        }

        # 市场指数配置
        self.market_indices = {
            "000001.SH": "上证指数",
            "399001.SZ": "深证成指",
            "399006.SZ": "创业板指",
            "000300.SH": "沪深300",
            "000905.SH": "中证500",
            "399905.SZ": "中证500",
            "000852.SH": "中证1000"
        }

        # 缓存真实板块数据
        self._cached_sectors = {}
        self._cache_timestamp = None
        self._cache_ttl = 3600  # 缓存1小时

        # 数据源状态
        self._data_source_status = {
            'last_update': None,
            'update_success': False,
            'error_count': 0,
            'total_sectors': 0,
            'total_stocks': 0
        }
    
    async def initialize_sectors(self) -> bool:
        """从真实数据源初始化板块数据"""
        try:
            logger.info("开始从真实数据源初始化板块数据...")

            # 1. 检查数据源连接状态
            if not await self._check_data_source_connection():
                logger.warning("数据源连接失败，使用备用板块数据")
                return await self._initialize_fallback_sectors()

            # 2. 获取真实行业板块数据
            industry_sectors = await self._fetch_real_sectors("industry")
            if industry_sectors:
                logger.info(f"获取到 {len(industry_sectors)} 个行业板块")
                for sector in industry_sectors:
                    await self._insert_sector_info(sector)
            else:
                logger.warning("未获取到行业板块数据")

            # 3. 获取真实概念板块数据
            concept_sectors = await self._fetch_real_sectors("concept")
            if concept_sectors:
                logger.info(f"获取到 {len(concept_sectors)} 个概念板块")
                for sector in concept_sectors:
                    await self._insert_sector_info(sector)
            else:
                logger.warning("未获取到概念板块数据")

            # 4. 数据质量检查
            total_sectors = len(industry_sectors or []) + len(concept_sectors or [])
            if total_sectors < self.data_quality_config['min_sector_count']:
                logger.warning(f"板块数量不足 ({total_sectors} < {self.data_quality_config['min_sector_count']})，使用备用数据")
                return await self._initialize_fallback_sectors()

            # 5. 更新状态
            self._data_source_status.update({
                'last_update': datetime.now(),
                'update_success': True,
                'error_count': 0,
                'total_sectors': total_sectors
            })

            logger.info(f"真实板块数据初始化完成，共创建 {total_sectors} 个板块")
            return True

        except Exception as e:
            logger.error(f"板块数据初始化失败: {e}")
            self._data_source_status['error_count'] += 1
            return await self._initialize_fallback_sectors()

    async def _check_data_source_connection(self) -> bool:
        """检查数据源连接状态"""
        try:
            # 获取最佳数据源
            best_source = self.data_source_manager.get_best_source()
            if not best_source:
                logger.warning("没有可用的数据源")
                return False

            # 测试连接
            if not best_source.test_connection():
                logger.warning("数据源连接测试失败")
                return False

            logger.info("数据源连接正常")
            return True

        except Exception as e:
            logger.error(f"检查数据源连接失败: {e}")
            return False

    async def _fetch_real_sectors(self, sector_type: str) -> List[SectorInfo]:
        """从真实数据源获取板块数据"""
        try:
            # 获取最佳数据源
            best_source = self.data_source_manager.get_best_source()
            if not best_source:
                raise Exception("没有可用的数据源")

            # 检查是否有板块数据获取方法
            if not hasattr(best_source, 'get_sector_list'):
                logger.warning("数据源不支持板块数据获取")
                return []

            # 获取板块列表
            raw_sectors = best_source.get_sector_list(sector_type)
            if not raw_sectors:
                logger.warning(f"未获取到 {sector_type} 板块数据")
                return []

            # 转换为标准格式
            sectors = []
            for raw_sector in raw_sectors:
                try:
                    sector_info = SectorInfo(
                        sector_code=raw_sector.get('code', ''),
                        sector_name=raw_sector.get('name', ''),
                        sector_type=sector_type,
                        parent_code=raw_sector.get('parent_code'),
                        level=raw_sector.get('level', 1),
                        is_active=True
                    )
                    sectors.append(sector_info)
                except Exception as e:
                    logger.warning(f"处理板块数据失败: {raw_sector}, 错误: {e}")
                    continue

            logger.info(f"成功获取 {len(sectors)} 个 {sector_type} 板块")
            return sectors

        except Exception as e:
            logger.error(f"获取 {sector_type} 板块数据失败: {e}")
            return []

    async def _initialize_fallback_sectors(self) -> bool:
        """使用备用板块数据初始化"""
        try:
            logger.info("使用备用板块数据初始化...")

            # 预定义的备用板块数据（申万一级行业分类）
            fallback_industry_sectors = {
                "BK0001": "农林牧渔", "BK0002": "采掘", "BK0003": "化工", "BK0004": "钢铁",
                "BK0005": "有色金属", "BK0006": "电子", "BK0007": "家用电器", "BK0008": "食品饮料",
                "BK0009": "纺织服装", "BK0010": "轻工制造", "BK0011": "医药生物", "BK0012": "公用事业",
                "BK0013": "交通运输", "BK0014": "房地产", "BK0015": "商业贸易", "BK0016": "休闲服务",
                "BK0017": "综合", "BK0018": "建筑材料", "BK0019": "建筑装饰", "BK0020": "电气设备",
                "BK0021": "国防军工", "BK0022": "计算机", "BK0023": "传媒", "BK0024": "通信",
                "BK0025": "银行", "BK0026": "非银金融", "BK0027": "汽车", "BK0028": "机械设备",
                "BK0029": "煤炭", "BK0030": "石油石化", "BK0031": "基础化工", "BK0032": "美容护理",
                "BK0033": "社会服务", "BK0034": "环保"
            }

            fallback_concept_sectors = {
                "BK1001": "新能源", "BK1002": "人工智能", "BK1003": "芯片概念", "BK1004": "5G概念",
                "BK1005": "新能源汽车", "BK1006": "光伏概念", "BK1007": "锂电池", "BK1008": "半导体",
                "BK1009": "云计算", "BK1010": "大数据", "BK1011": "物联网", "BK1012": "区块链",
                "BK1013": "虚拟现实", "BK1014": "生物医药", "BK1015": "医疗器械", "BK1016": "增强现实",
                "BK1017": "自动驾驶", "BK1018": "风电", "BK1019": "储能", "BK1020": "氢能源",
                "BK1021": "核电", "BK1022": "特高压", "BK1023": "充电桩", "BK1024": "工业4.0",
                "BK1025": "智能制造", "BK1026": "机器人", "BK1027": "无人机", "BK1028": "3D打印",
                "BK1029": "基因测序", "BK1030": "细胞治疗", "BK1031": "互联网医疗", "BK1032": "在线教育",
                "BK1033": "直播电商", "BK1034": "新零售", "BK1035": "共享经济", "BK1036": "数字经济",
                "BK1037": "网络安全", "BK1038": "信息安全", "BK1039": "量子通信", "BK1040": "卫星导航",
                "BK1041": "航天航空", "BK1042": "海洋经济", "BK1043": "乡村振兴", "BK1044": "京津冀",
                "BK1045": "长三角", "BK1046": "粤港澳大湾区", "BK1047": "一带一路", "BK1048": "自贸区",
                "BK1049": "雄安新区", "BK1050": "海南自贸港", "BK1051": "成渝双城", "BK1052": "RCEP",
                "BK1053": "专精特新", "BK1054": "北交所", "BK1055": "注册制", "BK1056": "REITs",
                "BK1057": "碳中和", "BK1058": "新基建", "BK1059": "数字货币", "BK1060": "工业互联网"
            }

            # 创建行业板块
            for sector_code, sector_name in fallback_industry_sectors.items():
                sector_info = SectorInfo(
                    sector_code=sector_code,
                    sector_name=sector_name,
                    sector_type="industry"
                )
                await self._insert_sector_info(sector_info)

            # 创建概念板块
            for sector_code, sector_name in fallback_concept_sectors.items():
                sector_info = SectorInfo(
                    sector_code=sector_code,
                    sector_name=sector_name,
                    sector_type="concept"
                )
                await self._insert_sector_info(sector_info)

            total_sectors = len(fallback_industry_sectors) + len(fallback_concept_sectors)
            logger.info(f"备用板块数据初始化完成，共创建 {total_sectors} 个板块")
            return True

        except Exception as e:
            logger.error(f"备用板块数据初始化失败: {e}")
            return False

    async def download_sector_data(self,
                                 sector_codes: Optional[List[str]] = None,
                                 days: int = 365,
                                 progress_callback: Optional[callable] = None) -> bool:
        """
        从真实数据源下载板块历史数据

        Args:
            sector_codes: 板块代码列表，None表示下载所有板块
            days: 下载天数
            progress_callback: 进度回调函数

        Returns:
            bool: 下载是否成功
        """
        try:
            if self.is_updating:
                logger.warning("板块数据正在更新中，请稍后再试")
                return False

            self.is_updating = True

            # 1. 检查数据源连接
            if not await self._check_data_source_connection():
                logger.error("数据源连接失败，无法下载板块数据")
                return False

            # 2. 获取要下载的板块列表
            if sector_codes is None:
                sector_list = self.get_sector_list()
                sector_codes = [s['sector_code'] for s in sector_list]

            if not sector_codes:
                logger.warning("没有可下载的板块")
                return False

            logger.info(f"开始从真实数据源下载 {len(sector_codes)} 个板块的历史数据，时间范围：{days}天")

            # 3. 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            success_count = 0
            total_count = len(sector_codes)
            retry_count = 0
            max_retries = self.data_quality_config['max_retry_count']

            for i, sector_code in enumerate(sector_codes):
                try:
                    # 更新进度
                    if progress_callback:
                        progress = (i / total_count) * 100
                        progress_callback(f"下载板块数据: {sector_code}", progress)

                    # 从真实数据源下载板块历史数据
                    sector_data = await self._download_real_sector_quotes(
                        sector_code, start_date, end_date
                    )

                    if sector_data:
                        # 数据质量检查
                        if await self._validate_sector_data(sector_data, sector_code):
                            # 存储到数据库
                            await self._insert_sector_quotes(sector_code, sector_data)
                            success_count += 1
                            retry_count = 0  # 重置重试计数
                        else:
                            logger.warning(f"板块 {sector_code} 数据质量检查失败")
                    else:
                        logger.warning(f"板块 {sector_code} 未获取到数据")

                    # 避免请求过快
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.warning(f"下载板块 {sector_code} 数据失败: {e}")
                    retry_count += 1

                    # 如果连续失败次数过多，暂停一段时间
                    if retry_count >= max_retries:
                        logger.warning(f"连续失败 {retry_count} 次，暂停 5 秒")
                        await asyncio.sleep(5)
                        retry_count = 0

                    continue

            # 4. 更新状态
            success_rate = success_count / total_count if total_count > 0 else 0
            self._data_source_status.update({
                'last_update': datetime.now(),
                'update_success': success_rate > 0.5,  # 成功率超过50%视为成功
                'total_sectors': success_count
            })

            logger.info(f"板块数据下载完成，成功 {success_count}/{total_count} 个板块 (成功率: {success_rate:.1%})")
            return success_count > 0

        except Exception as e:
            logger.error(f"下载板块数据失败: {e}")
            self._data_source_status['error_count'] += 1
            return False
        finally:
            self.is_updating = False

    async def _download_real_sector_quotes(self,
                                         sector_code: str,
                                         start_date: datetime,
                                         end_date: datetime) -> List[SectorQuote]:
        """从真实数据源下载板块行情数据"""
        try:
            # 获取最佳数据源
            best_source = self.data_source_manager.get_best_source()
            if not best_source:
                raise Exception("没有可用的数据源")

            # 检查是否支持板块数据获取
            if not hasattr(best_source, 'get_market_data'):
                logger.warning("数据源不支持市场数据获取")
                return await self._download_sector_quotes(sector_code, start_date, end_date)

            # 格式化日期
            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')

            # 获取板块行情数据
            market_data = best_source.get_market_data(
                symbol=sector_code,
                period='1d',
                start_date=start_date_str,
                end_date=end_date_str
            )

            if not market_data or not hasattr(market_data, 'data') or market_data.data.empty:
                logger.warning(f"板块 {sector_code} 未获取到真实行情数据，使用模拟数据")
                return await self._download_sector_quotes(sector_code, start_date, end_date)

            # 转换为SectorQuote格式
            quotes = []
            df = market_data.data

            for index, row in df.iterrows():
                try:
                    # 计算涨跌幅
                    change_pct = None
                    if 'change_pct' in row:
                        change_pct = row['change_pct']
                    elif 'close' in row and 'open' in row and row['open'] > 0:
                        change_pct = (row['close'] - row['open']) / row['open'] * 100

                    quote = SectorQuote(
                        sector_code=sector_code,
                        trade_date=index.strftime('%Y-%m-%d') if hasattr(index, 'strftime') else str(index),
                        open=float(row.get('open', 0)),
                        high=float(row.get('high', 0)),
                        low=float(row.get('low', 0)),
                        close=float(row.get('close', 0)),
                        volume=int(row.get('volume', 0)),
                        amount=float(row.get('amount', 0)),
                        change_pct=change_pct
                    )
                    quotes.append(quote)

                except Exception as e:
                    logger.warning(f"处理板块 {sector_code} 行情数据失败: {e}")
                    continue

            logger.debug(f"成功获取板块 {sector_code} 真实行情数据 {len(quotes)} 条")
            return quotes

        except Exception as e:
            logger.warning(f"获取板块 {sector_code} 真实行情数据失败: {e}，使用模拟数据")
            return await self._download_sector_quotes(sector_code, start_date, end_date)

    async def _validate_sector_data(self, quotes: List[SectorQuote], sector_code: str) -> bool:
        """验证板块数据质量"""
        try:
            if not quotes:
                return False

            # 检查数据数量
            if len(quotes) < 5:  # 至少5天数据
                logger.warning(f"板块 {sector_code} 数据量不足: {len(quotes)} 条")
                return False

            # 检查数据完整性
            valid_count = 0
            for quote in quotes:
                if (quote.open > 0 and quote.high > 0 and
                    quote.low > 0 and quote.close > 0 and
                    quote.high >= quote.low and
                    quote.high >= quote.open and quote.high >= quote.close and
                    quote.low <= quote.open and quote.low <= quote.close):
                    valid_count += 1

            valid_rate = valid_count / len(quotes)
            if valid_rate < 0.8:  # 有效数据率低于80%
                logger.warning(f"板块 {sector_code} 数据质量不佳: 有效率 {valid_rate:.1%}")
                return False

            logger.debug(f"板块 {sector_code} 数据质量检查通过: {valid_count}/{len(quotes)} 条有效")
            return True

        except Exception as e:
            logger.error(f"验证板块 {sector_code} 数据质量失败: {e}")
            return False

    async def update_sector_constituents(self,
                                       sector_code: str,
                                       stock_codes: Optional[List[str]] = None) -> bool:
        """
        从真实数据源更新板块成分股

        Args:
            sector_code: 板块代码
            stock_codes: 成分股代码列表，None表示从数据源获取

        Returns:
            bool: 更新是否成功
        """
        try:
            # 如果没有提供股票代码，从数据源获取
            if stock_codes is None:
                stock_codes = await self._fetch_real_sector_constituents(sector_code)

            if not stock_codes:
                logger.warning(f"板块 {sector_code} 没有获取到成分股数据")
                return False

            # 清除旧的成分股关系
            await self._clear_sector_constituents(sector_code)

            # 添加新的成分股关系
            success_count = 0
            for stock_code in stock_codes:
                try:
                    # 获取股票名称
                    stock_name = await self._get_stock_name_from_source(stock_code)

                    constituent = SectorConstituent(
                        sector_code=sector_code,
                        stock_code=stock_code,
                        stock_name=stock_name,
                        weight=0.0,  # 权重需要单独获取
                        in_date=datetime.now().strftime('%Y-%m-%d')
                    )

                    if await self._insert_sector_constituent(constituent):
                        success_count += 1

                except Exception as e:
                    logger.warning(f"添加成分股 {stock_code} 失败: {e}")
                    continue

            logger.info(f"更新板块 {sector_code} 成分股完成，成功 {success_count}/{len(stock_codes)} 只股票")
            return success_count > 0

        except Exception as e:
            logger.error(f"更新板块成分股失败: {e}")
            return False

    async def _fetch_real_sector_constituents(self, sector_code: str) -> List[str]:
        """从真实数据源获取板块成分股"""
        try:
            # 获取最佳数据源
            best_source = self.data_source_manager.get_best_source()
            if not best_source:
                logger.warning("没有可用的数据源")
                return []

            # 检查是否支持成分股获取
            if hasattr(best_source, 'get_sector_constituents'):
                constituents = best_source.get_sector_constituents(sector_code)
                if constituents:
                    return [c.get('stock_code', '') for c in constituents if c.get('stock_code')]

            # 如果数据源不支持，尝试其他方法
            if hasattr(best_source, 'get_stock_list_in_sector'):
                stock_list = best_source.get_stock_list_in_sector(sector_code)
                if stock_list:
                    return stock_list

            logger.warning(f"数据源不支持获取板块 {sector_code} 成分股")
            return []

        except Exception as e:
            logger.error(f"获取板块 {sector_code} 成分股失败: {e}")
            return []

    async def _get_stock_name_from_source(self, stock_code: str) -> str:
        """从数据源获取股票名称"""
        try:
            # 获取最佳数据源
            best_source = self.data_source_manager.get_best_source()
            if not best_source:
                return stock_code.split('.')[0]

            # 尝试获取股票信息
            if hasattr(best_source, 'get_stock_info'):
                stock_info = best_source.get_stock_info(stock_code)
                if stock_info and 'name' in stock_info:
                    return stock_info['name']

            # 如果无法获取，返回代码
            return stock_code.split('.')[0]

        except Exception as e:
            logger.warning(f"获取股票 {stock_code} 名称失败: {e}")
            return stock_code.split('.')[0]
    
    def get_sector_list(self, sector_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取板块列表
        
        Args:
            sector_type: 板块类型，None表示获取所有板块
            
        Returns:
            List[Dict]: 板块信息列表
        """
        try:
            sql = "SELECT * FROM sector_info WHERE is_active = 1"
            params = []
            
            if sector_type:
                sql += " AND sector_type = ?"
                params.append(sector_type)
            
            sql += " ORDER BY sector_code"
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            sectors = []
            for row in results:
                sectors.append({
                    'sector_code': row[0],
                    'sector_name': row[1],
                    'sector_type': row[2],
                    'parent_code': row[3],
                    'level': row[4],
                    'is_active': bool(row[5])
                })
            
            return sectors
            
        except Exception as e:
            logger.error(f"获取板块列表失败: {e}")
            return []
    
    def get_sector_constituents(self, sector_code: str) -> List[Dict[str, Any]]:
        """
        获取板块成分股
        
        Args:
            sector_code: 板块代码
            
        Returns:
            List[Dict]: 成分股信息列表
        """
        try:
            sql = """
            SELECT sc.*, si.stock_name 
            FROM sector_constituents sc
            LEFT JOIN stock_info si ON sc.stock_code = si.stock_code
            WHERE sc.sector_code = ? AND (sc.out_date IS NULL OR sc.out_date > date('now'))
            ORDER BY sc.weight DESC, sc.stock_code
            """
            
            results = self.db_manager.execute_query(sql, (sector_code,))
            
            constituents = []
            for row in results:
                constituents.append({
                    'sector_code': row[0],
                    'stock_code': row[1],
                    'stock_name': row[2] or row[6],  # 优先使用关联表中的名称
                    'weight': row[3],
                    'in_date': row[4],
                    'out_date': row[5]
                })
            
            return constituents
            
        except Exception as e:
            logger.error(f"获取板块成分股失败: {e}")
            return []
    
    async def _insert_sector_info(self, sector_info: SectorInfo) -> bool:
        """插入板块信息"""
        try:
            sql = """
            INSERT OR REPLACE INTO sector_info 
            (sector_code, sector_name, sector_type, parent_code, level, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            
            with self.db_manager.get_connection() as conn:
                conn.execute(sql, (
                    sector_info.sector_code,
                    sector_info.sector_name,
                    sector_info.sector_type,
                    sector_info.parent_code,
                    sector_info.level,
                    sector_info.is_active
                ))
                conn.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"插入板块信息失败: {e}")
            return False

    async def _download_sector_quotes(self,
                                    sector_code: str,
                                    start_date: datetime,
                                    end_date: datetime) -> List[SectorQuote]:
        """下载板块行情数据（模拟实现）"""
        try:
            # 模拟生成板块行情数据
            quotes = []
            current_date = start_date
            base_price = 1000.0  # 基础价格

            while current_date <= end_date:
                # 跳过周末
                if current_date.weekday() < 5:
                    # 模拟价格波动
                    change_pct = (hash(f"{sector_code}{current_date}") % 200 - 100) / 1000.0  # -10% to +10%
                    close_price = base_price * (1 + change_pct)

                    quote = SectorQuote(
                        sector_code=sector_code,
                        trade_date=current_date.strftime('%Y-%m-%d'),
                        open=close_price * 0.995,
                        high=close_price * 1.02,
                        low=close_price * 0.98,
                        close=close_price,
                        volume=int(abs(hash(f"{sector_code}{current_date}volume")) % 1000000),
                        amount=close_price * int(abs(hash(f"{sector_code}{current_date}amount")) % 1000000),
                        change_pct=change_pct * 100
                    )
                    quotes.append(quote)
                    base_price = close_price  # 更新基础价格

                current_date += timedelta(days=1)

            return quotes

        except Exception as e:
            logger.error(f"下载板块{sector_code}行情数据失败: {e}")
            return []

    async def _insert_sector_quotes(self, sector_code: str, quotes: List[SectorQuote]) -> bool:
        """插入板块行情数据"""
        try:
            sql = """
            INSERT OR REPLACE INTO sector_quotes
            (sector_code, trade_date, open, high, low, close, volume, amount, change_pct)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            with self.db_manager.get_connection() as conn:
                for quote in quotes:
                    conn.execute(sql, (
                        quote.sector_code,
                        quote.trade_date,
                        quote.open,
                        quote.high,
                        quote.low,
                        quote.close,
                        quote.volume,
                        quote.amount,
                        quote.change_pct
                    ))
                conn.commit()

            logger.debug(f"插入板块{sector_code}行情数据{len(quotes)}条")
            return True

        except Exception as e:
            logger.error(f"插入板块行情数据失败: {e}")
            return False

    async def _insert_sector_constituent(self, constituent: SectorConstituent) -> bool:
        """插入板块成分股"""
        try:
            sql = """
            INSERT OR REPLACE INTO sector_constituents
            (sector_code, stock_code, stock_name, weight, in_date, out_date)
            VALUES (?, ?, ?, ?, ?, ?)
            """

            with self.db_manager.get_connection() as conn:
                conn.execute(sql, (
                    constituent.sector_code,
                    constituent.stock_code,
                    constituent.stock_name,
                    constituent.weight,
                    constituent.in_date,
                    constituent.out_date
                ))
                conn.commit()

            return True

        except Exception as e:
            logger.error(f"插入板块成分股失败: {e}")
            return False

    async def _clear_sector_constituents(self, sector_code: str) -> bool:
        """清除板块成分股"""
        try:
            sql = "DELETE FROM sector_constituents WHERE sector_code = ?"

            with self.db_manager.get_connection() as conn:
                conn.execute(sql, (sector_code,))
                conn.commit()

            return True

        except Exception as e:
            logger.error(f"清除板块成分股失败: {e}")
            return False

    def get_sector_quotes(self,
                         sector_code: str,
                         start_date: Optional[str] = None,
                         end_date: Optional[str] = None,
                         limit: Optional[int] = None) -> pd.DataFrame:
        """
        获取板块行情数据

        Args:
            sector_code: 板块代码
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制条数

        Returns:
            pd.DataFrame: 行情数据
        """
        try:
            sql = "SELECT * FROM sector_quotes WHERE sector_code = ?"
            params = [sector_code]

            if start_date:
                sql += " AND trade_date >= ?"
                params.append(start_date)

            if end_date:
                sql += " AND trade_date <= ?"
                params.append(end_date)

            sql += " ORDER BY trade_date DESC"

            if limit:
                sql += f" LIMIT {limit}"

            results = self.db_manager.execute_query(sql, tuple(params))

            if not results:
                return pd.DataFrame()

            # 转换为DataFrame - 根据实际查询结果调整列数
            if results and len(results[0]) == 11:
                columns = ['id', 'sector_code', 'trade_date', 'open', 'high', 'low',
                          'close', 'volume', 'amount', 'change_pct', 'update_time']
            else:
                # 如果列数不匹配，使用通用列名
                columns = [f'col_{i}' for i in range(len(results[0]))] if results else []

            df = pd.DataFrame(results, columns=columns)
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df.set_index('trade_date', inplace=True)

            return df

        except Exception as e:
            logger.error(f"获取板块行情数据失败: {e}")
            return pd.DataFrame()

    def calculate_sector_return(self,
                              sector_code: str,
                              start_date: str,
                              end_date: str) -> Optional[float]:
        """
        计算板块收益率

        Args:
            sector_code: 板块代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Optional[float]: 收益率，失败返回None
        """
        try:
            df = self.get_sector_quotes(sector_code, start_date, end_date)

            if df.empty or len(df) < 2:
                return None

            # 按日期排序
            df = df.sort_index()

            start_price = df['close'].iloc[0]
            end_price = df['close'].iloc[-1]

            if start_price <= 0:
                return None

            return (end_price - start_price) / start_price

        except Exception as e:
            logger.error(f"计算板块收益率失败: {e}")
            return None
