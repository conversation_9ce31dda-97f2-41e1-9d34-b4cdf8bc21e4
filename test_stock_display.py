#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票数据显示修复效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt6.QtCore import QTimer
from src.services.data_service import DataService
from src.data_sources.manager import DataSourceManager
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.base import DataSourceConfig
from src.ui.components.stock_list_widget import StockListWidget
from src.utils.logger import setup_logger, get_logger

# 设置日志
setup_logger("test", level="INFO", log_to_console=True)
logger = get_logger(__name__)


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("股票数据显示测试")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("正在初始化...")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_button = QPushButton("测试实时数据更新")
        self.test_button.clicked.connect(self.test_real_time_update)
        layout.addWidget(self.test_button)
        
        # 初始化数据服务
        self.init_data_service()
        
        # 创建股票列表控件
        self.stock_list = StockListWidget(data_service=self.data_service)
        layout.addWidget(self.stock_list)
        
        # 连接信号
        self.stock_list.stocks_loaded.connect(self.on_stocks_loaded)
        self.stock_list.stock_selected.connect(self.on_stock_selected)
        
        logger.info("测试窗口初始化完成")
    
    def init_data_service(self):
        """初始化数据服务"""
        try:
            # 创建数据源管理器
            self.data_manager = DataSourceManager()
            
            # 创建XtData配置
            config = DataSourceConfig(
                name="XtData",
                enabled=True,
                timeout=30,
                retry_times=3,
                auto_reconnect=True,
                config={
                    "host": "127.0.0.1",
                    "port": 58610
                }
            )
            
            # 创建XtData数据源
            xtdata_source = XtDataAdapter(config)
            self.data_manager.add_source("xtdata", xtdata_source, priority=10)
            
            # 创建数据服务
            self.data_service = DataService(
                data_source_manager=self.data_manager,
                use_real_data=True
            )
            
            self.status_label.setText("数据服务初始化完成")
            logger.info("数据服务初始化完成")
            
        except Exception as e:
            self.status_label.setText(f"数据服务初始化失败: {e}")
            logger.error(f"数据服务初始化失败: {e}")
    
    def on_stocks_loaded(self, count):
        """股票加载完成"""
        self.status_label.setText(f"已加载 {count} 只股票")
        logger.info(f"股票加载完成，共 {count} 只")
    
    def on_stock_selected(self, symbol):
        """股票选择事件"""
        self.status_label.setText(f"选中股票: {symbol}")
        logger.info(f"选中股票: {symbol}")
    
    def test_real_time_update(self):
        """测试实时数据更新"""
        try:
            self.status_label.setText("正在测试实时数据更新...")
            
            # 手动触发实时数据更新
            if hasattr(self.stock_list, '_update_real_time_data'):
                self.stock_list._update_real_time_data()
                self.status_label.setText("实时数据更新测试完成")
            else:
                self.status_label.setText("实时数据更新方法不存在")
                
        except Exception as e:
            self.status_label.setText(f"实时数据更新测试失败: {e}")
            logger.error(f"实时数据更新测试失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("股票数据显示测试")
    app.setApplicationVersion("1.0.0")
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
