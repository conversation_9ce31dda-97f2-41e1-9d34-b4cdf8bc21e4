#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块筛选界面控件
实现需求文档中的板块相对强弱筛选功能的用户界面
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QComboBox, QSpinBox, QDoubleSpinBox, 
    QCheckBox, QPushButton, QTableWidget, QTableWidgetItem,
    QGroupBox, QTabWidget, QSplitter, QProgressBar,
    QTextEdit, QDateEdit, QSlider, QFrame
)
from PyQt6.QtCore import Qt, QTimer, QDate, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from ...services.data_service import DataService
from ...engines.sector_screening import SectorScreeningEngine, SectorScreeningParams
from ...services.sector_manager import SectorManager
from ...services.return_calculator import ReturnCalculator
from ...services.market_index_manager import MarketIndexManager
from ...database.manager import DatabaseManager
from ...data_sources.manager import DataSourceManager
from ...utils.logger import get_logger
from ..utils.error_handler import UIErrorHandler
from ..utils.export_manager import ExportManager

logger = get_logger(__name__)


class SectorScreeningWidget(QWidget):
    """板块筛选控件"""
    
    # 信号定义
    screening_started = pyqtSignal(dict)  # 筛选开始
    screening_completed = pyqtSignal(dict)  # 筛选完成
    sector_selected = pyqtSignal(str)  # 板块选择
    
    def __init__(self, parent: Optional[QWidget] = None, data_service=None):
        """
        初始化板块筛选控件

        Args:
            parent: 父控件
            data_service: 数据服务实例
        """
        super().__init__(parent)

        # 数据服务
        self.data_service = data_service or DataService()
        
        # 筛选引擎
        self.db_manager = DatabaseManager("data/stock_analysis.db")
        self.data_source_manager = DataSourceManager()
        self.sector_manager = SectorManager(self.db_manager, self.data_source_manager)
        self.market_index_manager = MarketIndexManager(self.db_manager, self.data_source_manager)
        self.return_calculator = ReturnCalculator(
            self.db_manager,
            self.sector_manager,
            self.market_index_manager
        )
        self.screening_engine = SectorScreeningEngine(
            self.sector_manager,
            self.return_calculator
        )
        
        # 状态变量
        self.is_screening = False
        self.screening_results: List[Dict] = []

        # 错误处理和导出管理
        self.error_handler = UIErrorHandler(self)
        self.export_manager = ExportManager(self)
        
        # UI组件引用
        self.config_tabs: Optional[QTabWidget] = None
        self.results_table: Optional[QTableWidget] = None
        self.progress_bar: Optional[QProgressBar] = None
        self.log_text: Optional[QTextEdit] = None
        self.start_button: Optional[QPushButton] = None
        self.stop_button: Optional[QPushButton] = None
        
        # 配置参数控件
        self.params = {}
        
        self._init_ui()
        self._load_default_config()
        
        logger.info("板块筛选控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("📊 板块相对强弱筛选")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分：配置区域
        config_widget = self._create_config_widget()
        splitter.addWidget(config_widget)
        
        # 下半部分：结果区域
        results_widget = self._create_results_widget()
        splitter.addWidget(results_widget)
        
        # 设置分割比例
        splitter.setSizes([300, 400])
        
        layout.addWidget(splitter)
    
    def _create_config_widget(self) -> QWidget:
        """创建配置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建标签页
        self.config_tabs = QTabWidget()
        
        # 基础参数配置标签页
        basic_tab = self._create_basic_config_tab()
        self.config_tabs.addTab(basic_tab, "⚙️ 基础参数")
        
        # 板块选择配置标签页
        sector_tab = self._create_sector_config_tab()
        self.config_tabs.addTab(sector_tab, "🏢 板块选择")
        
        # 筛选条件配置标签页
        filter_tab = self._create_filter_config_tab()
        self.config_tabs.addTab(filter_tab, "🎯 筛选条件")
        
        layout.addWidget(self.config_tabs)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("🚀 开始板块筛选")
        self.start_button.clicked.connect(self._start_screening)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("⏹️ 停止筛选")
        self.stop_button.clicked.connect(self._stop_screening)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        
        # 预设方案
        preset_button = QPushButton("📋 预设方案")
        preset_button.clicked.connect(self._show_preset_dialog)
        button_layout.addWidget(preset_button)
        
        # 保存配置
        save_button = QPushButton("💾 保存配置")
        save_button.clicked.connect(self._save_config)
        button_layout.addWidget(save_button)
        
        layout.addLayout(button_layout)
        
        return widget
    
    def _create_basic_config_tab(self) -> QWidget:
        """创建基础参数配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 时间参数组
        time_group = QGroupBox("时间参数")
        time_layout = QGridLayout(time_group)
        
        # 开始日期
        time_layout.addWidget(QLabel("开始日期:"), 0, 0)
        self.params['start_date'] = QDateEdit()
        self.params['start_date'].setDate(QDate.currentDate().addDays(-30))
        self.params['start_date'].setCalendarPopup(True)
        time_layout.addWidget(self.params['start_date'], 0, 1)
        
        # 结束日期
        time_layout.addWidget(QLabel("结束日期:"), 0, 2)
        self.params['end_date'] = QDateEdit()
        self.params['end_date'].setDate(QDate.currentDate())
        self.params['end_date'].setCalendarPopup(True)
        time_layout.addWidget(self.params['end_date'], 0, 3)
        
        # 市场指数
        time_layout.addWidget(QLabel("市场指数:"), 1, 0)
        self.params['market_index'] = QComboBox()
        self.params['market_index'].addItems([
            "000001.SH - 上证指数",
            "399001.SZ - 深证成指", 
            "399006.SZ - 创业板指",
            "000300.SH - 沪深300"
        ])
        time_layout.addWidget(self.params['market_index'], 1, 1, 1, 3)
        
        layout.addWidget(time_group)
        
        # 筛选参数组
        filter_group = QGroupBox("筛选参数")
        filter_layout = QGridLayout(filter_group)
        
        # 最大板块数量
        filter_layout.addWidget(QLabel("最大板块数量:"), 0, 0)
        self.params['max_sectors'] = QSpinBox()
        self.params['max_sectors'].setRange(1, 50)
        self.params['max_sectors'].setValue(10)
        filter_layout.addWidget(self.params['max_sectors'], 0, 1)
        
        # 最小相对强弱度
        filter_layout.addWidget(QLabel("最小相对强弱度(%):"), 0, 2)
        self.params['min_relative_strength'] = QDoubleSpinBox()
        self.params['min_relative_strength'].setRange(-50.0, 50.0)
        self.params['min_relative_strength'].setSingleStep(0.1)
        self.params['min_relative_strength'].setValue(0.0)
        filter_layout.addWidget(self.params['min_relative_strength'], 0, 3)
        
        layout.addWidget(filter_group)
        layout.addStretch()
        
        return widget
    
    def _create_sector_config_tab(self) -> QWidget:
        """创建板块选择配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 板块类型选择
        type_group = QGroupBox("板块类型选择")
        type_layout = QGridLayout(type_group)
        
        type_layout.addWidget(QLabel("行业板块:"), 0, 0)
        self.params['include_industry'] = QCheckBox()
        self.params['include_industry'].setChecked(True)
        type_layout.addWidget(self.params['include_industry'], 0, 1)
        
        type_layout.addWidget(QLabel("概念板块:"), 0, 2)
        self.params['include_concept'] = QCheckBox()
        self.params['include_concept'].setChecked(True)
        type_layout.addWidget(self.params['include_concept'], 0, 3)
        
        layout.addWidget(type_group)
        
        # 具体板块选择
        selection_group = QGroupBox("具体板块选择")
        selection_layout = QVBoxLayout(selection_group)
        
        # 全选/反选
        select_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self._select_all_sectors)
        select_layout.addWidget(select_all_btn)
        
        deselect_all_btn = QPushButton("反选")
        deselect_all_btn.clicked.connect(self._deselect_all_sectors)
        select_layout.addWidget(deselect_all_btn)
        
        select_layout.addStretch()
        selection_layout.addLayout(select_layout)
        
        # 动态加载板块列表
        self.sector_checkboxes = {}
        self._load_sector_list(selection_layout)

        layout.addWidget(selection_group)
        
        layout.addStretch()
        return widget

    def _load_sector_list(self, parent_layout):
        """动态加载板块列表"""
        try:
            # 从数据服务获取板块列表
            if hasattr(self, 'data_service') and self.data_service:
                sectors = self.data_service.get_sector_list()
            else:
                # 如果没有数据服务，使用默认板块
                sectors = self._get_default_sectors()

            if not sectors:
                sectors = self._get_default_sectors()

            # 按类型分组
            industry_sectors = [s for s in sectors if s.get('type') == 'industry']
            concept_sectors = [s for s in sectors if s.get('type') == 'concept']

            # 创建分组显示
            if industry_sectors:
                industry_group = QGroupBox("行业板块")
                industry_layout = QGridLayout(industry_group)

                for i, sector in enumerate(industry_sectors):
                    sector_name = sector.get('name', sector.get('code', ''))
                    checkbox = QCheckBox(sector_name)
                    checkbox.setChecked(True)
                    self.sector_checkboxes[sector_name] = checkbox
                    industry_layout.addWidget(checkbox, i // 4, i % 4)

                parent_layout.addWidget(industry_group)

            if concept_sectors:
                concept_group = QGroupBox("概念板块")
                concept_layout = QGridLayout(concept_group)

                for i, sector in enumerate(concept_sectors):
                    sector_name = sector.get('name', sector.get('code', ''))
                    checkbox = QCheckBox(sector_name)
                    checkbox.setChecked(True)
                    self.sector_checkboxes[sector_name] = checkbox
                    concept_layout.addWidget(checkbox, i // 4, i % 4)

                parent_layout.addWidget(concept_group)

            logger.info(f"成功加载 {len(sectors)} 个板块（行业: {len(industry_sectors)}, 概念: {len(concept_sectors)}）")

        except Exception as e:
            logger.error(f"加载板块列表失败: {e}")
            # 使用默认板块作为备用
            self._load_default_sectors(parent_layout)

    def _get_default_sectors(self):
        """获取默认板块列表"""
        return [
            {"code": "银行", "name": "银行", "type": "industry"},
            {"code": "房地产", "name": "房地产", "type": "industry"},
            {"code": "食品饮料", "name": "食品饮料", "type": "industry"},
            {"code": "医药生物", "name": "医药生物", "type": "industry"},
            {"code": "电子", "name": "电子", "type": "industry"},
            {"code": "计算机", "name": "计算机", "type": "industry"},
            {"code": "新能源", "name": "新能源", "type": "concept"},
            {"code": "人工智能", "name": "人工智能", "type": "concept"},
            {"code": "芯片", "name": "芯片", "type": "concept"},
            {"code": "5G", "name": "5G", "type": "concept"},
            {"code": "新基建", "name": "新基建", "type": "concept"},
            {"code": "碳中和", "name": "碳中和", "type": "concept"}
        ]

    def _load_default_sectors(self, parent_layout):
        """加载默认板块列表"""
        sectors = self._get_default_sectors()

        sector_grid = QGridLayout()
        for i, sector in enumerate(sectors):
            sector_name = sector.get('name', sector.get('code', ''))
            sector_type = sector.get('type', 'unknown')
            checkbox = QCheckBox(f"{sector_name} ({sector_type})")
            checkbox.setChecked(True)
            self.sector_checkboxes[sector_name] = checkbox
            sector_grid.addWidget(checkbox, i // 3, i % 3)

        parent_layout.addLayout(sector_grid)
    
    def _create_filter_config_tab(self) -> QWidget:
        """创建筛选条件配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 排序方式
        sort_group = QGroupBox("排序方式")
        sort_layout = QGridLayout(sort_group)
        
        sort_layout.addWidget(QLabel("排序字段:"), 0, 0)
        self.params['sort_by'] = QComboBox()
        self.params['sort_by'].addItems([
            "relative_strength - 相对强弱度",
            "sector_return - 板块涨幅",
            "percentile - 百分位排名"
        ])
        sort_layout.addWidget(self.params['sort_by'], 0, 1)
        
        sort_layout.addWidget(QLabel("排序方向:"), 0, 2)
        self.params['sort_order'] = QComboBox()
        self.params['sort_order'].addItems(["降序", "升序"])
        sort_layout.addWidget(self.params['sort_order'], 0, 3)
        
        layout.addWidget(sort_group)
        
        # 高级筛选
        advanced_group = QGroupBox("高级筛选")
        advanced_layout = QGridLayout(advanced_group)
        
        advanced_layout.addWidget(QLabel("启用百分位筛选:"), 0, 0)
        self.params['enable_percentile_filter'] = QCheckBox()
        advanced_layout.addWidget(self.params['enable_percentile_filter'], 0, 1)
        
        advanced_layout.addWidget(QLabel("最小百分位:"), 0, 2)
        self.params['min_percentile'] = QSpinBox()
        self.params['min_percentile'].setRange(0, 100)
        self.params['min_percentile'].setValue(70)
        advanced_layout.addWidget(self.params['min_percentile'], 0, 3)
        
        layout.addWidget(advanced_group)
        layout.addStretch()
        
        return widget

    def _create_results_widget(self) -> QWidget:
        """创建结果展示区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 结果标题和统计
        header_layout = QHBoxLayout()

        results_label = QLabel("📈 板块筛选结果")
        results_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        header_layout.addWidget(results_label)

        header_layout.addStretch()

        # 导出按钮
        export_button = QPushButton("📤 导出结果")
        export_button.clicked.connect(self._export_results)
        header_layout.addWidget(export_button)

        layout.addLayout(header_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(7)
        self.results_table.setHorizontalHeaderLabels([
            "排名", "板块代码", "板块名称", "板块类型",
            "板块涨幅(%)", "相对强弱度(%)", "百分位排名"
        ])

        # 设置表格属性
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.itemSelectionChanged.connect(self._on_sector_selection_changed)

        layout.addWidget(self.results_table)

        # 日志区域
        log_label = QLabel("📝 筛选日志")
        log_label.setFont(QFont("Microsoft YaHei UI", 9, QFont.Weight.Bold))
        layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)

        return widget

    def _load_default_config(self):
        """加载默认配置"""
        # 设置默认时间范围为最近30天
        end_date = QDate.currentDate()
        start_date = end_date.addDays(-30)

        self.params['start_date'].setDate(start_date)
        self.params['end_date'].setDate(end_date)

        self._add_log("已加载默认配置")

    def _start_screening(self):
        """开始板块筛选"""
        if self.is_screening:
            return

        # 获取配置参数
        config = self._get_screening_config()

        # 验证参数
        if not self._validate_config(config):
            return

        # 更新UI状态
        self.is_screening = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 清空之前的结果
        self.results_table.setRowCount(0)
        self.screening_results.clear()

        self._add_log("开始执行板块筛选...")
        self._add_log(f"时间范围：{config['start_date']} 至 {config['end_date']}")
        self._add_log(f"市场指数：{config['market_index']}")

        # 发送筛选开始信号
        self.screening_started.emit(config)

        # 模拟筛选过程（实际应该调用真实的筛选引擎）
        self._simulate_screening(config)

        logger.info("板块筛选任务已启动")

    def _stop_screening(self):
        """停止板块筛选"""
        if not self.is_screening:
            return

        self.is_screening = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        self._add_log("板块筛选已停止")
        logger.info("板块筛选已停止")

    def _simulate_screening(self, config: Dict[str, Any]):
        """模拟板块筛选过程"""
        # 模拟数据
        mock_sectors = [
            {"code": "BK0001", "name": "银行", "type": "industry", "return": 5.2, "relative_strength": 2.1},
            {"code": "BK0002", "name": "房地产", "type": "industry", "return": 3.8, "relative_strength": 0.7},
            {"code": "BK0003", "name": "食品饮料", "type": "industry", "return": 7.5, "relative_strength": 4.4},
            {"code": "BK0004", "name": "医药生物", "type": "industry", "return": 6.1, "relative_strength": 3.0},
            {"code": "BK0005", "name": "电子", "type": "industry", "return": 8.9, "relative_strength": 5.8},
            {"code": "BK0006", "name": "计算机", "type": "industry", "return": 9.2, "relative_strength": 6.1},
            {"code": "BK0007", "name": "新能源", "type": "concept", "return": 12.3, "relative_strength": 9.2},
            {"code": "BK0008", "name": "人工智能", "type": "concept", "return": 10.7, "relative_strength": 7.6},
            {"code": "BK0009", "name": "芯片", "type": "concept", "return": 11.5, "relative_strength": 8.4},
            {"code": "BK0010", "name": "5G", "type": "concept", "return": 4.2, "relative_strength": 1.1}
        ]

        # 筛选强势板块（相对强弱度 > 0）
        strong_sectors = [s for s in mock_sectors if s["relative_strength"] > config.get("min_relative_strength", 0.0)]

        # 排序
        strong_sectors.sort(key=lambda x: x["relative_strength"], reverse=True)

        # 限制数量
        max_sectors = config.get("max_sectors", 10)
        strong_sectors = strong_sectors[:max_sectors]

        # 计算百分位排名
        for i, sector in enumerate(strong_sectors):
            sector["rank"] = i + 1
            sector["percentile"] = round((len(strong_sectors) - i) / len(strong_sectors) * 100, 1)

        # 模拟进度更新
        total_steps = len(strong_sectors)
        for i, sector in enumerate(strong_sectors):
            progress = int((i + 1) / total_steps * 100)
            self.progress_bar.setValue(progress)

            # 添加到结果
            self.screening_results.append(sector)

            # 更新表格
            self._add_sector_to_table(sector)

            # 添加日志
            self._add_log(f"发现强势板块：{sector['name']} (相对强弱度: {sector['relative_strength']:.1f}%)")

            # 模拟延迟
            QTimer.singleShot(200 * i, lambda: None)

        # 完成筛选
        QTimer.singleShot(200 * total_steps + 500, self._complete_screening)

    def _complete_screening(self):
        """完成板块筛选"""
        if not self.is_screening:
            return

        # 更新UI状态
        self.is_screening = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        # 添加完成日志
        count = len(self.screening_results)
        self._add_log(f"板块筛选完成！共筛选出 {count} 个强势板块")

        # 发送完成信号
        self.screening_completed.emit({
            'results': self.screening_results,
            'count': count
        })

        logger.info(f"板块筛选完成，结果数量：{count}")

    def _add_sector_to_table(self, sector: Dict[str, Any]):
        """添加板块到结果表格"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        # 填充数据
        self.results_table.setItem(row, 0, QTableWidgetItem(str(sector["rank"])))
        self.results_table.setItem(row, 1, QTableWidgetItem(sector["code"]))
        self.results_table.setItem(row, 2, QTableWidgetItem(sector["name"]))
        self.results_table.setItem(row, 3, QTableWidgetItem(sector["type"]))
        self.results_table.setItem(row, 4, QTableWidgetItem(f"{sector['return']:.2f}"))
        self.results_table.setItem(row, 5, QTableWidgetItem(f"{sector['relative_strength']:.2f}"))
        self.results_table.setItem(row, 6, QTableWidgetItem(f"{sector['percentile']:.1f}"))

        # 设置颜色
        if sector["relative_strength"] > 5.0:
            color = QColor(220, 255, 220)  # 浅绿色
        elif sector["relative_strength"] > 2.0:
            color = QColor(255, 255, 220)  # 浅黄色
        else:
            color = QColor(255, 240, 240)  # 浅红色

        for col in range(7):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(color)

        # 自动调整列宽
        self.results_table.resizeColumnsToContents()

    def _get_screening_config(self) -> Dict[str, Any]:
        """获取筛选配置"""
        # 获取市场指数代码
        market_text = self.params['market_index'].currentText()
        market_index = market_text.split(' - ')[0]

        # 获取板块类型
        sector_types = []
        if self.params['include_industry'].isChecked():
            sector_types.append("industry")
        if self.params['include_concept'].isChecked():
            sector_types.append("concept")

        # 获取选中的板块
        selected_sectors = []
        for sector_name, checkbox in self.sector_checkboxes.items():
            if checkbox.isChecked():
                selected_sectors.append(sector_name)

        return {
            'start_date': self.params['start_date'].date().toString('yyyy-MM-dd'),
            'end_date': self.params['end_date'].date().toString('yyyy-MM-dd'),
            'market_index': market_index,
            'sector_types': sector_types,
            'selected_sectors': selected_sectors,
            'max_sectors': self.params['max_sectors'].value(),
            'min_relative_strength': self.params['min_relative_strength'].value(),
            'sort_by': self.params['sort_by'].currentText().split(' - ')[0],
            'sort_order': self.params['sort_order'].currentText(),
            'enable_percentile_filter': self.params['enable_percentile_filter'].isChecked(),
            'min_percentile': self.params['min_percentile'].value()
        }

    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置参数"""
        # 检查时间范围
        start_date = QDate.fromString(config['start_date'], 'yyyy-MM-dd')
        end_date = QDate.fromString(config['end_date'], 'yyyy-MM-dd')

        if start_date >= end_date:
            self._add_log("错误：开始日期必须早于结束日期")
            return False

        # 检查板块类型
        if not config['sector_types']:
            self._add_log("错误：至少选择一种板块类型")
            return False

        # 检查选中的板块
        if not config['selected_sectors']:
            self._add_log("错误：至少选择一个板块")
            return False

        return True

    def _add_log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def _on_sector_selection_changed(self):
        """板块选择变化事件"""
        current_row = self.results_table.currentRow()
        if current_row >= 0 and current_row < len(self.screening_results):
            sector = self.screening_results[current_row]
            self.sector_selected.emit(sector["code"])
            self._add_log(f"已选择板块：{sector['name']}")

    def _select_all_sectors(self):
        """全选板块"""
        for checkbox in self.sector_checkboxes.values():
            checkbox.setChecked(True)
        self._add_log("已全选所有板块")

    def _deselect_all_sectors(self):
        """反选板块"""
        for checkbox in self.sector_checkboxes.values():
            checkbox.setChecked(False)
        self._add_log("已取消选择所有板块")

    def _show_preset_dialog(self):
        """显示预设方案对话框"""
        # TODO: 实现预设方案对话框
        self._add_log("预设方案功能开发中...")

    def _save_config(self):
        """保存配置"""
        # TODO: 实现配置保存功能
        self._add_log("配置保存功能开发中...")

    def _export_results(self):
        """导出结果"""
        if not self.screening_results:
            self.error_handler.handle_warning("没有可导出的筛选结果")
            return

        try:
            # 准备导出数据
            export_data = {
                'sector_results': self.screening_results,
                'parameters': self._get_screening_config(),
                'statistics': {
                    'total_sectors': len(self.screening_results),
                    'export_time': datetime.now().isoformat(),
                    'screening_type': '板块相对强弱筛选'
                }
            }

            # 执行导出
            success = self.export_manager.export_results(export_data, "excel")
            if success:
                self._add_log("开始导出筛选结果...")
            else:
                self.error_handler.handle_error("export_failed", "导出启动失败")

        except Exception as e:
            self.error_handler.handle_error("export_failed", str(e))
            self._add_log(f"导出失败: {e}")

    def get_screening_results(self) -> List[Dict[str, Any]]:
        """获取筛选结果"""
        return self.screening_results.copy()

    def clear_results(self):
        """清空结果"""
        self.results_table.setRowCount(0)
        self.screening_results.clear()
        self.log_text.clear()
        self._add_log("已清空筛选结果")
