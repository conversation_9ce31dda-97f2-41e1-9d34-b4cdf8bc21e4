# 股票数据显示问题修复报告

## 问题概述

本次修复解决了威科夫相对强弱选股系统中的两个主要问题：

1. **股票名称显示问题**：股票名称列显示不清楚或被截断
2. **股票数据异常问题**：最新价、涨跌幅等关键字段显示为0

## 修复内容

### 1. 股票名称显示优化

#### 修改文件：`src/ui/components/stock_list_widget.py`

**问题分析：**
- 股票名称列设置为`Stretch`模式，但没有设置合适的最小宽度
- 在窗口大小变化时，名称列可能被压缩导致显示不全

**修复方案：**
- 将名称列的调整模式从`Stretch`改为`Interactive`，允许用户手动调整列宽
- 设置名称列的默认宽度为120像素，确保常见股票名称能够完整显示
- 移除了重复的列宽设置代码

```python
# 修复前
header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 名称

# 修复后  
header.setSectionResizeMode(1, QHeaderView.ResizeMode.Interactive)  # 名称
self.table.setColumnWidth(1, 120)  # 名称 - 设置合适的宽度
```

### 2. 股票数据获取和显示优化

#### 修改文件：`src/services/data_service.py`

**问题分析：**
- 系统启动时故意将股票价格设置为0，避免大量API调用
- 缺少实时数据更新机制
- 股票名称获取逻辑不完善，直接使用代码作为名称

**修复方案：**

1. **改进股票基本信息获取**
   - 添加`_get_stock_name()`方法，正确获取股票名称
   - 添加`_get_stock_sector()`方法，根据股票代码判断所属板块
   - 在加载股票列表时调用这些方法获取完整信息

2. **添加实时数据更新功能**
   - 新增`update_stock_real_time_data()`方法，支持批量更新股票实时数据
   - 使用XtData API的`get_full_tick()`方法获取最新行情
   - 正确计算涨跌幅：`(当前价 - 前收价) / 前收价 * 100`

#### 修改文件：`src/data_sources/xtdata_adapter.py`

**问题分析：**
- 在模拟数据模式下，股票名称获取方法可能失败
- 缺少对模拟数据的完善支持

**修复方案：**
- 改进`_get_stock_name()`方法，增加模拟数据模式的支持
- 添加`_get_mock_stock_name()`方法，为常见股票提供预定义名称
- 根据股票代码规则生成合理的模拟名称

#### 修改文件：`src/ui/components/stock_list_widget.py`

**问题分析：**
- 缺少实时数据更新机制
- 刷新功能不完善

**修复方案：**
1. **添加实时数据更新定时器**
   - 创建`update_timer`，每10秒自动更新一次实时数据
   - 延迟启动定时器，等待初始数据加载完成

2. **实现实时数据更新方法**
   - `_update_real_time_data()`：更新当前显示股票的实时数据
   - 限制更新数量为20只股票，提高性能
   - 更新后自动刷新表格显示

3. **改进刷新功能**
   - 在手动刷新时立即更新实时数据
   - 提供启动/停止实时更新的控制方法

## API使用说明

### XtData API集成

根据迅投在线文档，本次修复正确使用了以下API：

1. **获取股票基本信息**
   ```python
   info = xtdata.get_instrument_detail(symbol)
   ```

2. **获取实时行情数据**
   ```python
   tick_data = xtdata.get_full_tick([stock_code])
   ```

3. **数据字段说明**
   - `lastPrice`：最新价
   - `lastClose`：前收盘价
   - `volume`：成交量

### 数据更新逻辑

1. **启动时**：加载股票基本信息，价格暂时为0
2. **实时更新**：每10秒更新一次当前显示的股票数据
3. **手动刷新**：立即更新所有数据

## 测试验证

创建了测试脚本`test_stock_display.py`用于验证修复效果：

```bash
python test_stock_display.py
```

测试内容包括：
- 股票列表加载
- 股票名称显示
- 实时数据更新
- 用户交互功能

## 性能优化

1. **限制更新数量**：每次只更新前20只显示的股票
2. **合理的更新频率**：10秒更新一次，避免过于频繁的API调用
3. **延迟启动**：等待初始数据加载完成后再启动实时更新
4. **错误处理**：完善的异常处理，避免单个股票更新失败影响整体功能

## 配置说明

在`config.yaml`中确保以下配置：

```yaml
data_download:
  use_real_data: true  # 启用真实数据

data_sources:
  xtdata:
    enabled: true
    config:
      host: "127.0.0.1"
      port: 58610
```

## 注意事项

1. **XtData客户端**：确保MiniQMT客户端正在运行
2. **网络连接**：确保能够连接到数据服务器
3. **数据权限**：确保有相应的行情数据权限
4. **模拟模式**：在无法连接真实数据源时，系统会自动切换到模拟数据模式

## 后续建议

1. **数据缓存**：考虑添加本地数据缓存，减少API调用
2. **更多字段**：可以添加更多股票信息字段，如市盈率、市净率等
3. **自定义更新频率**：允许用户自定义实时数据更新频率
4. **数据质量监控**：添加数据质量检查和异常数据过滤
