#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块筛选UI功能测试
测试新增的板块筛选界面功能
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import QTimer

from src.ui.components.sector_screening_widget import SectorScreeningWidget
from src.ui.main_window import MainWindow
from src.utils.logger import get_logger

logger = get_logger(__name__)


class SectorScreeningTestWindow(QMainWindow):
    """板块筛选测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("板块筛选功能测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建板块筛选控件
        from src.services.data_service import DataService
        data_service = DataService()
        self.sector_widget = SectorScreeningWidget(data_service=data_service)
        layout.addWidget(self.sector_widget)
        
        # 连接信号
        self.sector_widget.screening_started.connect(self._on_screening_started)
        self.sector_widget.screening_completed.connect(self._on_screening_completed)
        self.sector_widget.sector_selected.connect(self._on_sector_selected)
        
        logger.info("板块筛选测试窗口初始化完成")
    
    def _on_screening_started(self, config):
        """筛选开始事件"""
        logger.info(f"板块筛选开始: {config}")
        print(f"📊 板块筛选开始")
        print(f"   时间范围: {config['start_date']} 至 {config['end_date']}")
        print(f"   市场指数: {config['market_index']}")
        print(f"   板块类型: {config['sector_types']}")
        print(f"   最大板块数: {config['max_sectors']}")
    
    def _on_screening_completed(self, results):
        """筛选完成事件"""
        count = results.get('count', 0)
        logger.info(f"板块筛选完成，结果数量: {count}")
        print(f"✅ 板块筛选完成！")
        print(f"   筛选出 {count} 个强势板块")
        
        # 显示前5个结果
        screening_results = results.get('results', [])
        if screening_results:
            print(f"   前5个强势板块:")
            for i, sector in enumerate(screening_results[:5]):
                print(f"   {i+1}. {sector['name']} - 相对强弱度: {sector['relative_strength']:.2f}%")
    
    def _on_sector_selected(self, sector_code):
        """板块选择事件"""
        logger.info(f"板块选择: {sector_code}")
        print(f"🎯 已选择板块: {sector_code}")


def test_sector_screening_widget():
    """测试板块筛选控件"""
    print("🧪 测试板块筛选控件功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = SectorScreeningTestWindow()
    test_window.show()
    
    print("✅ 板块筛选控件创建成功")
    print("📋 测试功能:")
    print("   1. 基础参数配置（时间范围、市场指数）")
    print("   2. 板块选择配置（行业板块、概念板块）")
    print("   3. 筛选条件配置（排序方式、高级筛选）")
    print("   4. 筛选结果展示（表格、日志）")
    print("   5. 交互功能（开始/停止筛选、导出结果）")
    print("\n💡 请在界面中测试各项功能...")
    
    # 运行应用
    sys.exit(app.exec())


def test_main_window_integration():
    """测试主窗口集成"""
    print("🧪 测试主窗口板块筛选集成")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    print("✅ 主窗口创建成功")
    print("📋 集成功能测试:")
    print("   1. 选股系统标签页中的板块筛选子标签页")
    print("   2. 菜单栏中的板块筛选菜单项")
    print("   3. 工具栏中的板块筛选按钮")
    print("   4. 板块筛选与智能选股的联动")
    print("   5. 状态栏和进度反馈")
    print("\n💡 请测试以下操作:")
    print("   - 点击菜单栏 '选股' -> '板块筛选'")
    print("   - 点击工具栏 '板块筛选' 按钮")
    print("   - 使用快捷键 Ctrl+B")
    print("   - 在选股系统标签页中切换子标签页")
    
    # 运行应用
    sys.exit(app.exec())


def test_screening_workflow():
    """测试筛选工作流"""
    print("🧪 测试板块筛选工作流")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = SectorScreeningTestWindow()
    test_window.show()
    
    # 自动化测试流程
    def auto_test():
        print("🤖 开始自动化测试...")
        
        # 设置测试参数
        widget = test_window.sector_widget
        
        # 设置时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        print(f"   设置时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        
        # 设置筛选参数
        widget.params['max_sectors'].setValue(8)
        widget.params['min_relative_strength'].setValue(1.0)
        
        print(f"   设置筛选参数: 最大板块数=8, 最小相对强弱度=1.0%")
        
        # 启动筛选
        print("   启动板块筛选...")
        widget._start_screening()
    
    # 延迟执行自动测试
    QTimer.singleShot(2000, auto_test)
    
    print("✅ 自动化测试准备就绪")
    print("⏰ 2秒后开始自动测试...")
    
    # 运行应用
    sys.exit(app.exec())


def main():
    """主测试函数"""
    print("🚀 板块筛选UI功能测试套件")
    print("=" * 60)
    print("本测试验证第19周新增的板块筛选界面功能")
    print("=" * 60)
    
    test_options = {
        '1': ('测试板块筛选控件', test_sector_screening_widget),
        '2': ('测试主窗口集成', test_main_window_integration),
        '3': ('测试筛选工作流', test_screening_workflow)
    }
    
    print("\n📋 可用测试选项:")
    for key, (desc, _) in test_options.items():
        print(f"   {key}. {desc}")
    
    choice = input("\n请选择测试选项 (1-3, 默认1): ").strip() or '1'
    
    if choice in test_options:
        desc, test_func = test_options[choice]
        print(f"\n🎯 执行测试: {desc}")
        print("-" * 40)
        test_func()
    else:
        print("❌ 无效选项，执行默认测试")
        test_sector_screening_widget()


if __name__ == "__main__":
    main()
